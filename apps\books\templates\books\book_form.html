{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}{% if form.instance.pk %}Editar Livro{% else %}Novo Livro{% endif %} - Livros - Project Nix{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card shadow-sm border-0">
                <div class="card-body">
                    <h1 class="h4 mb-4">{% if form.instance.pk %}Editar Livro{% else %}Novo Livro{% endif %}</h1>
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        {{ form|crispy }}
                        <div class="d-flex justify-content-end gap-2 mt-4">
                            <a href="{% url 'books:book_list' %}" class="btn btn-outline-secondary">Cancelar</a>
                            <button type="submit" class="btn btn-primary"><PERSON>var</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 
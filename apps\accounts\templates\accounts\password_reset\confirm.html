{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}Confirmar Nova Senha - {{ block.super }}{% endblock %}

{% block content %}
<div class="container my-5">
    <!-- Header da <PERSON> -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-1 text-sans text-body">
                        <i class="fas fa-shield-alt me-2 text-django-green"></i>Nova Senha
                    </h1>
                    <p class="text-secondary mb-0 text-body">Defina sua nova senha de acesso</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <!-- Card de Confirmação -->
            <div class="card-django border-0 shadow-sm">
                <div class="profile-card-header bg-success text-light text-center">
                    <h4 class="mb-0 text-sans text-body">
                        <i class="fas fa-shield-alt me-2"></i>Redefinir Senha
                    </h4>
                </div>
                <div class="card-body profile-card-body">
                    <div class="text-center mb-4">
                        <i class="fas fa-key fa-3x text-success mb-3"></i>
                        <p class="text-secondary text-body">
                            Digite o código enviado para <strong class="text-django-green">{{ email }}</strong>
                            e defina sua nova senha.
                        </p>
                    </div>

                    <!-- Mensagens -->
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {% if message.tags == 'success' %}
                                    <i class="fas fa-check-circle me-2"></i>
                                {% elif message.tags == 'error' %}
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                {% elif message.tags == 'warning' %}
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                {% else %}
                                    <i class="fas fa-info-circle me-2"></i>
                                {% endif %}
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <!-- Formulário de Confirmação -->
                    <form method="post" class="needs-validation form-django" novalidate style="display: block;">
                        {% csrf_token %}
                        <input type="hidden" name="email" value="{{ email }}">

                        <!-- Código de Verificação -->
                        <div class="mb-3">
                            <label for="code" class="form-label text-sans">Código de Verificação</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-shield-alt text-django-green"></i>
                                </span>
                                <input type="text" class="form-control" id="code" name="code"
                                       placeholder="123456" maxlength="6" pattern="[0-9]{6}"
                                       inputmode="numeric" required>
                                <div class="invalid-feedback">
                                    Digite o código de 6 dígitos.
                                </div>
                            </div>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Código de 6 dígitos enviado por email
                            </div>
                        </div>

                        <!-- Nova Senha -->
                        <div class="mb-3" style="display: block !important;">
                            <label for="new_password" class="form-label text-sans">
                                <i class="fas fa-lock me-1"></i>Nova Senha
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock text-django-green"></i>
                                </span>
                                <input type="password" class="form-control" id="new_password" name="new_password"
                                       placeholder="Digite sua nova senha" autocomplete="new-password" 
                                       minlength="8" required>
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('new_password')">
                                    <i class="fas fa-eye" id="new_password_icon"></i>
                                </button>
                            </div>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Mínimo de 8 caracteres
                            </div>
                            <div class="invalid-feedback">
                                Senha deve ter pelo menos 8 caracteres.
                            </div>
                        </div>

                        <!-- Confirmar Senha -->
                        <div class="mb-3" style="display: block !important;">
                            <label for="confirm_password" class="form-label text-sans">
                                <i class="fas fa-lock me-1"></i>Confirmar Nova Senha
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock text-django-green"></i>
                                </span>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password"
                                       placeholder="Confirme sua nova senha" autocomplete="new-password" 
                                       minlength="8" required>
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                                    <i class="fas fa-eye" id="confirm_password_icon"></i>
                                </button>
                            </div>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Digite a mesma senha novamente
                            </div>
                            <div class="invalid-feedback">
                                As senhas devem coincidir.
                            </div>
                        </div>

                        <div class="d-grid gap-2 mt-4">
                            <button type="submit" class="btn btn-success btn-lg text-sans">
                                <i class="fas fa-check me-2"></i>Redefinir Senha
                            </button>
                        </div>
                    </form>

                    <!-- Dicas de Segurança -->
                    <div class="mt-4">
                        <div class="alert alert-info border-0">
                            <div class="d-flex align-items-start">
                                <i class="fas fa-shield-alt text-info me-2 mt-1"></i>
                                <div class="small">
                                    <strong>Dicas de Segurança:</strong>
                                    <ul class="mb-0 mt-1">
                                        <li>Use pelo menos 8 caracteres</li>
                                        <li>Combine letras, números e símbolos</li>
                                        <li>Evite informações pessoais</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Links Úteis -->
            <div class="text-center mt-4">
                <p class="text-secondary">
                    Não recebeu o código?
                    <a href="{% url 'accounts:password_reset' %}" class="text-django-green text-decoration-none">
                        <i class="fas fa-redo me-1"></i>Solicitar Novo Código
                    </a>
                </p>
                <p class="text-secondary">
                    Lembrou da senha?
                    <a href="{% url 'accounts:login' %}" class="text-django-green text-decoration-none">
                        <i class="fas fa-sign-in-alt me-1"></i>Fazer Login
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus no campo de código
    const codeField = document.getElementById('code');
    if (codeField) {
        codeField.focus();

        // Formatar entrada (apenas números)
        codeField.addEventListener('input', function() {
            this.value = this.value.replace(/\D/g, '');
            if (this.value.length > 6) {
                this.value = this.value.slice(0, 6);
            }
        });

        // Permitir apenas números
        codeField.addEventListener('keypress', function(e) {
            if (!/\d/.test(e.key) && !['Backspace', 'Delete', 'Tab', 'Enter'].includes(e.key)) {
                e.preventDefault();
            }
        });
    }

    // Elementos do formulário
    const newPasswordField = document.getElementById('new_password');
    const confirmPasswordField = document.getElementById('confirm_password');
    const form = document.querySelector('.needs-validation');

    // Validação de senhas em tempo real
    function validatePasswords() {
        const newPassword = newPasswordField.value;
        const confirmPassword = confirmPasswordField.value;

        // Validar nova senha
        newPasswordField.classList.remove('is-valid', 'is-invalid');
        if (newPassword.length >= 8) {
            newPasswordField.classList.add('is-valid');
        } else if (newPassword.length > 0) {
            newPasswordField.classList.add('is-invalid');
        }

        // Validar confirmação
        confirmPasswordField.classList.remove('is-valid', 'is-invalid');
        if (confirmPassword.length > 0) {
            if (newPassword === confirmPassword && newPassword.length >= 8) {
                confirmPasswordField.classList.add('is-valid');
                confirmPasswordField.setCustomValidity('');
            } else {
                confirmPasswordField.classList.add('is-invalid');
                confirmPasswordField.setCustomValidity('As senhas não coincidem');
            }
        }
    }

    if (newPasswordField && confirmPasswordField) {
        newPasswordField.addEventListener('input', validatePasswords);
        confirmPasswordField.addEventListener('input', validatePasswords);
    }

    // Validação de formulário
    if (form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            } else {
                // Feedback visual no envio
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Redefinindo...';
                    submitBtn.disabled = true;
                }
            }
            form.classList.add('was-validated');
        });
    }

    // Auto-dismiss de alertas após 5 segundos
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert.parentElement) {
                alert.classList.remove('show');
                setTimeout(() => alert.remove(), 150);
            }
        }, 5000);
    });
});

// Função para mostrar/ocultar senha
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '_icon');

    if (field.type === 'password') {
        field.type = 'text';
        icon.className = 'fas fa-eye-slash';
    } else {
        field.type = 'password';
        icon.className = 'fas fa-eye';
    }
}
</script>
{% endblock %}

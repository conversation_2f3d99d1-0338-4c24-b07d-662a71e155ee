{% extends 'base.html' %}
{% block content %}
<h1>Detalhes do Usuário</h1>
<ul>
    <li>Usuário: {{ user.username }}</li>
    <li>Email: {{ user.email }}</li>
    <li>Grupos: {% for group in user.groups.all %}{{ group.name }}{% if not forloop.last %}, {% endif %}{% endfor %}</li>
</ul>
<a href="{% url 'config:user_update' user.pk %}">Editar</a> |
<a href="{% url 'config:user_delete' user.pk %}">Deletar</a> |
<a href="{% url 'config:user_list' %}">Voltar</a>
{% endblock %}
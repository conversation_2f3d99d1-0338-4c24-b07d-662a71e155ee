#
# This file is autogenerated by pip-compile with Python 3.12
# by the following command:
#
#    pip-compile --output-file=requirements.txt requirements.in
#
asgiref==3.9.1
    # via
    #   django
    #   django-axes
    #   django-cors-headers
bleach==6.2.0
    # via -r requirements.in
certifi==2025.7.14
    # via requests
charset-normalizer==3.4.2
    # via requests
colorama==0.4.6
    # via pytest
coverage[toml]==7.9.2
    # via pytest-cov
crispy-bootstrap5==2023.10
    # via -r requirements.in
dj-database-url==3.0.1
    # via -r requirements.in
django==5.2.4
    # via
    #   -r requirements.in
    #   crispy-bootstrap5
    #   dj-database-url
    #   django-axes
    #   django-cors-headers
    #   django-crispy-forms
    #   django-csp
    #   django-debug-toolbar
    #   django-tinymce
    #   djangorestframework
    #   drf-yasg
django-axes==6.5.2
    # via -r requirements.in
django-cors-headers==4.7.0
    # via -r requirements.in
django-crispy-forms==2.4
    # via
    #   -r requirements.in
    #   crispy-bootstrap5
django-csp==3.8
    # via -r requirements.in
django-debug-toolbar==4.4.6
    # via -r requirements.in
django-tinymce==4.1.0
    # via -r requirements.in
djangorestframework==3.16.0
    # via
    #   -r requirements.in
    #   drf-yasg
drf-yasg==1.21.10
    # via -r requirements.in
factory-boy==3.3.3
    # via -r requirements.in
faker==37.4.2
    # via factory-boy
gunicorn==21.2.0
    # via -r requirements.in
idna==3.10
    # via requests
inflection==0.5.1
    # via drf-yasg
iniconfig==2.1.0
    # via pytest
mysql-connector-python==8.4.0
    # via -r requirements.in
packaging==25.0
    # via
    #   drf-yasg
    #   gunicorn
    #   pytest
pillow==10.4.0
    # via -r requirements.in
pluggy==1.6.0
    # via pytest
psutil==6.1.1
    # via -r requirements.in
psycopg2-binary==2.9.10
    # via -r requirements.in
pytest==7.4.4
    # via
    #   -r requirements.in
    #   pytest-cov
    #   pytest-django
pytest-cov==4.1.0
    # via -r requirements.in
pytest-django==4.11.1
    # via -r requirements.in
python-dotenv==1.1.1
    # via -r requirements.in
python-json-logger==2.0.7
    # via -r requirements.in
pytz==2025.2
    # via drf-yasg
pyyaml==6.0.2
    # via drf-yasg
requests==2.32.4
    # via -r requirements.in
sqlparse==0.5.3
    # via
    #   django
    #   django-debug-toolbar
tzdata==2025.2
    # via
    #   django
    #   faker
uritemplate==4.2.0
    # via drf-yasg
urllib3==2.5.0
    # via requests
webencodings==0.5.1
    # via bleach
whitenoise==6.9.0
    # via -r requirements.in
pytest>=7.0
pytest-django>=4.0
coverage

{% load static %}

<!-- Comments List Snippet -->
<div class="comments-list">
    {% for comment in article.comments.all %}
        {% if comment.is_approved %}
        <div class="comment-item mb-4" id="comment-{{ comment.id }}">
            <div class="d-flex">
                <!-- Avatar -->
                <div class="flex-shrink-0 me-3">
                    {% if comment.user and comment.user.avatar %}
                        <img src="{{ comment.user.avatar.url }}" class="rounded-circle" width="48" height="48" alt="{{ comment.author_name }}">
                    {% else %}
                        <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                            <i class="fas fa-user text-white"></i>
                        </div>
                    {% endif %}
                </div>
                <!-- Comment Content -->
                <div class="flex-grow-1">
                    <div class="comment-header mb-2">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1">
                                    {% if comment.website %}
                                        <a href="{{ comment.website }}" target="_blank" rel="nofollow noopener" class="text-decoration-none">
                                            {{ comment.author_name }}
                                            <i class="fas fa-external-link-alt fa-xs"></i>
                                        </a>
                                    {% else %}
                                        {{ comment.author_name }}
                                    {% endif %}
                                    {% if comment.user and comment.user.is_staff %}
                                        <span class="badge bg-primary ms-1">Staff</span>
                                    {% endif %}
                                </h6>
                                <small class="text-muted">
                                    <i class="fas fa-clock"></i>
                                    {{ comment.created_at|timesince }} atrás
                                </small>
                            </div>
                            <!-- Comment Actions -->
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li>
                                        <button class="dropdown-item" onclick="toggleReplyForm({{ comment.id }})">
                                            <i class="fas fa-reply"></i> Responder
                                        </button>
                                    </li>
                                    <li>
                                        <button class="dropdown-item" onclick="copyCommentLink({{ comment.id }})">
                                            <i class="fas fa-link"></i> Copiar link
                                        </button>
                                    </li>
                                    {% if user.is_staff %}
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item text-warning" href="{% url 'articles:moderate_comment_action' comment.id %}">
                                            <i class="fas fa-shield-alt"></i> Moderar
                                        </a>
                                    </li>
                                    {% endif %}
                                </ul>
                            </div>
                        </div>
                    </div>
                    <!-- Comment Text -->
                    <div class="comment-content mb-3">
                        {{ comment.content|linebreaks }}
                    </div>
                    <!-- Reply Button -->
                    {% if article.allow_comments and comment.can_be_replied %}
                    <div class="comment-actions mb-3">
                        <button class="btn btn-outline-primary btn-sm" onclick="toggleReplyForm({{ comment.id }})">
                            <i class="fas fa-reply"></i> Responder
                            {% if comment.reply_count > 0 %} ({{ comment.reply_count }}) {% endif %}
                        </button>
                    </div>
                    {% endif %}
                    <!-- Reply Form -->
                    {% if article.allow_comments and comment.can_be_replied %}
                    <div class="reply-form-container" id="reply-form-{{ comment.id }}" style="display: {% if reply_form and reply_parent_id == comment.id %}block{% else %}none{% endif %};">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-reply"></i> Responder a {{ comment.author_name }}
                                </h6>
                                <div id="reply-feedback-{{ comment.id }}" aria-live="polite"></div>
                                <form method="post" action="{% url 'articles:add_reply' article.slug comment.id %}" class="reply-form" data-comment-id="{{ comment.id }}">
                                    {% csrf_token %}
                                    {% if reply_form and reply_parent_id == comment.id %}
                                        {% if reply_form.errors or reply_form.non_field_errors %}
                                            <div class="alert alert-danger">
                                                {% for field in reply_form %}
                                                    {% for error in field.errors %}
                                                        <div><strong>{{ field.label }}:</strong> {{ error }}</div>
                                                    {% endfor %}
                                                {% endfor %}
                                                {% for error in reply_form.non_field_errors %}
                                                    <div>{{ error }}</div>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-floating mb-2">
                                                    {{ reply_form.name }}
                                                    <label for="{{ reply_form.name.id_for_label }}">Nome *</label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-floating mb-2">
                                                    {{ reply_form.email }}
                                                    <label for="{{ reply_form.email.id_for_label }}">Email *</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-floating mb-3">
                                            {{ reply_form.content }}
                                            <label for="{{ reply_form.content.id_for_label }}">Resposta *</label>
                                        </div>
                                        {{ reply_form.website_url }}
                                    {% else %}
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-floating mb-2">
                                                    <input type="text" class="form-control form-control-sm" name="name" placeholder="Seu nome" required {% if user.is_authenticated %}value="{{ user.get_full_name|default:user.username }}" readonly{% endif %} aria-label="Nome">
                                                    <label>Nome *</label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-floating mb-2">
                                                    <input type="email" class="form-control form-control-sm" name="email" placeholder="Seu email" required {% if user.is_authenticated %}value="{{ user.email }}" readonly{% endif %} aria-label="Email">
                                                    <label>Email *</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-floating mb-3">
                                            <textarea class="form-control form-control-sm" name="content" placeholder="Sua resposta..." rows="3" required aria-label="Resposta"></textarea>
                                            <label>Resposta *</label>
                                        </div>
                                        <input type="hidden" name="website_url" value="">
                                    {% endif %}
                                    <div class="d-flex justify-content-end gap-2">
                                        <button type="button" class="btn btn-secondary btn-sm" onclick="toggleReplyForm({{ comment.id }})" aria-label="Cancelar resposta">Cancelar</button>
                                        <button type="submit" class="btn btn-primary btn-sm" aria-label="Enviar resposta"><i class="fas fa-paper-plane"></i> Enviar Resposta</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    <!-- Replies -->
                    {% for reply in comment.get_replies|slice:":3" %}
                        {% if reply.is_approved %}
                        <div class="reply-item mt-3 ps-4 border-start border-2" id="comment-{{ reply.id }}">
                            <div class="d-flex">
                                <div class="flex-shrink-0 me-3">
                                    {% if reply.user and reply.user.avatar %}
                                        <img src="{{ reply.user.avatar.url }}" class="rounded-circle" width="32" height="32" alt="{{ reply.author_name }}">
                                    {% else %}
                                        <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                                            <i class="fas fa-user text-white fa-sm"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="flex-grow-1">
                                    <div class="reply-header mb-2">
                                        <h6 class="mb-1 h6">
                                            <i class="fas fa-reply fa-sm me-1"></i>
                                            {% if reply.website %}
                                                <a href="{{ reply.website }}" target="_blank" rel="nofollow noopener" class="text-decoration-none">
                                                    {{ reply.author_name }}
                                                    <i class="fas fa-external-link-alt fa-xs"></i>
                                                </a>
                                            {% else %}
                                                {{ reply.author_name }}
                                            {% endif %}
                                            {% if reply.user and reply.user.is_staff %}
                                                <span class="badge bg-primary ms-1">Staff</span>
                                            {% endif %}
                                        </h6>
                                        <small class="text-muted"><i class="fas fa-clock"></i> {{ reply.created_at|timesince }} atrás</small>
                                    </div>
                                    <div class="reply-content">{{ reply.content|linebreaks }}</div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}
    {% empty %}
    <div class="text-center py-5">
        <i class="fas fa-comments fa-3x text-secondary mb-3"></i>
        <h3 class="text-secondary text-sans text-body">Nenhum comentário encontrado</h3>
        <p class="text-secondary text-body">Ainda não há comentários aprovados.</p>
    </div>
    {% endfor %}
    
    <!-- Show more comments link -->
    {% if article.comment_count > 5 %}
    <div class="text-center mt-4">
        <a href="{% url 'articles:comment_list' article.slug %}" class="btn btn-outline-primary">
            <i class="fas fa-comments"></i>
            Ver todos os {{ article.comment_count }} comentários
        </a>
    </div>
    {% endif %}
</div>

<style>
.comment-item {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 1.5rem;
}

.comment-item:last-child {
    border-bottom: none;
}

.reply-item {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.375rem;
    margin-top: 1rem;
}

.comment-content, .reply-content {
    line-height: 1.6;
}

.reply-form-container {
    margin-top: 1rem;
}

.comment-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.dropdown-toggle::after {
    display: none;
}

@media (max-width: 768px) {
    .reply-item {
        margin-left: 0;
        padding-left: 1rem;
    }
}
</style>

<script>
function toggleReplyForm(commentId) {
    const replyForm = document.getElementById(`reply-form-${commentId}`);
    if (replyForm.style.display === 'none' || replyForm.style.display === '') {
        replyForm.style.display = 'block';
        // Focar no textarea
        const textarea = replyForm.querySelector('textarea[name="content"]');
        if (textarea) {
            textarea.focus();
        }
    } else {
        replyForm.style.display = 'none';
    }
}

function copyCommentLink(commentId) {
    const url = window.location.href.split('#')[0] + '#comment-' + commentId;
    navigator.clipboard.writeText(url).then(function() {
        // Mostrar feedback
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i> Copiado!';
        button.classList.add('text-success');
        
        setTimeout(() => {
            button.innerHTML = originalText;
            button.classList.remove('text-success');
        }, 2000);
    }).catch(function() {
        alert('Erro ao copiar link. Tente novamente.');
    });
}

// AJAX form submission
document.addEventListener('DOMContentLoaded', function() {
    // Main comment form
    const commentForm = document.getElementById('comment-form');
    if (commentForm) {
        commentForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitCommentForm(this, 'comment');
        });
    }
    
    // Reply forms
    const replyForms = document.querySelectorAll('.reply-form');
    replyForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            submitCommentForm(this, 'reply');
        });
    });
});

function submitCommentForm(form, type) {
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    // Show loading
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Enviando...';
    submitBtn.disabled = true;
    
    fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': formData.get('csrfmiddlewaretoken')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('success', data.message);
            
            if (type === 'comment') {
                form.reset();
                // Update comment count
                const countBadge = document.getElementById('comment-count');
                if (countBadge && data.is_approved) {
                    const currentCount = parseInt(countBadge.textContent);
                    countBadge.textContent = currentCount + 1;
                }
            } else {
                // Hide reply form and reset
                const replyContainer = form.closest('.reply-form-container');
                replyContainer.style.display = 'none';
                form.reset();
            }
            
            // Reload if approved automatically
            if (data.is_approved) {
                setTimeout(() => {
                    location.reload();
                }, 1500);
            }
        } else {
            let errorMessage = 'Erro ao enviar. Verifique os dados.';
            if (data.errors) {
                const errors = Object.values(data.errors).flat();
                errorMessage = errors.join(' ');
            }
            showMessage('danger', errorMessage);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('danger', 'Erro de conexão. Tente novamente.');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

function showMessage(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Insert at top of comments section
    const commentsSection = document.querySelector('.comments-section');
    commentsSection.insertBefore(alertDiv, commentsSection.firstChild);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>

<div class="comment" id="comment-{{ comment.id }}">
    <div class="comment-header">
        <strong>{{ comment.name }}</strong>
        <span class="text-muted small">{{ comment.created_at|date:"d/m/Y H:i" }}</span>
    </div>
    <div class="comment-body">{{ comment.content|linebreaksbr }}</div>
    <div class="comment-actions">
        {% if comment.can_reply %}
            <button class="btn btn-link btn-sm reply-btn" data-comment-id="{{ comment.id }}">Responder</button>
        {% endif %}
    </div>
    <div id="replies-for-{{ comment.id }}">
        {% if comment.children.exists %}
            <button class="btn btn-outline-secondary btn-sm btn-load-replies" data-comment-id="{{ comment.id }}">Ver respostas ({{ comment.children.count }})</button>
        {% endif %}
    </div>
    <div id="reply-feedback-{{ comment.id }}"></div>
</div>

{% extends 'config/base_config_page.html' %}
{% load static %}
{% load config_extras %}

{% block config_title %}Configuração de Email{% endblock %}
{% block page_icon %}<i class="fas fa-envelope me-2"></i>{% endblock %}
{% block page_title %}Configuração de Email{% endblock %}

{% block page_content %}
<!-- Status e Informações -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Status do Sistema de Email
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <div class="mb-3">
                            {% if email_configured %}
                                <i class="fas fa-check-circle fa-3x text-success"></i>
                            {% else %}
                                <i class="fas fa-exclamation-triangle fa-3x text-warning"></i>
                            {% endif %}
                        </div>
                        <h6>
                            {% if email_configured %}
                                <span class="text-success">Configurado</span>
                            {% else %}
                                <span class="text-warning">Não Configurado</span>
                            {% endif %}
                        </h6>
                    </div>
                    <div class="col-md-9">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-2">
                                    <strong>Backend:</strong>
                                    <span class="ms-2">{{ current_backend }}</span>
                                </div>
                                <div class="mb-2">
                                    <strong>Servidor:</strong>
                                    <span class="ms-2">{{ email_config.email_host|default:"Não configurado" }}</span>
                                </div>
                                <div class="mb-2">
                                    <strong>Porta:</strong>
                                    <span class="ms-2">{{ email_config.email_port|default:"N/A" }}</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-2">
                                    <strong>Usuário:</strong>
                                    <span class="ms-2">{{ email_config.email_host_user|default:"Não configurado" }}</span>
                                </div>
                                <div class="mb-2">
                                    <strong>Email Padrão:</strong>
                                    <span class="ms-2">{{ email_config.default_from_email|default:"Não configurado" }}</span>
                                </div>
                                <div class="mb-2">
                                    <strong>Segurança:</strong>
                                    <span class="ms-2">
                                        {% if email_config.email_use_tls or email_config.email_use_ssl %}
                                            <i class="fas fa-lock text-success"></i> TLS/SSL
                                        {% else %}
                                            <i class="fas fa-unlock text-warning"></i> Sem criptografia
                                        {% endif %}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Configuração Principal -->
    <div class="col-12 col-lg-8">
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cog me-2"></i>Configurações do Servidor
                </h5>
            </div>
            <div class="card-body">
                <form method="post" class="config-form">
                    {% csrf_token %}
                    
                    <!-- Exibir erros gerais do formulário -->
                    {% if form.errors %}
                        <div class="alert alert-danger mb-3">
                            <h6 class="alert-heading">
                                <i class="fas fa-exclamation-triangle me-2"></i>Erros no formulário:
                            </h6>
                            <ul class="mb-0">
                                {% for field, errors in form.errors.items %}
                                    {% if field != '__all__' %}
                                        {% for error in errors %}
                                            <li><strong>{{ field|title }}:</strong> {{ error }}</li>
                                        {% endfor %}
                                    {% endif %}
                                {% endfor %}
                                {% for error in form.non_field_errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                    {% endif %}
                    
                    <!-- Backend de Email -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <label for="{{ form.email_backend.id_for_label }}" class="form-label">
                                <i class="fas fa-server me-1"></i>Backend de Email
                            </label>
                            {{ form.email_backend }}
                            {% if form.email_backend.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email_backend.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                Escolha o tipo de backend para envio de emails
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.email_host.id_for_label }}" class="form-label">
                                <i class="fas fa-server me-1"></i>Servidor SMTP
                            </label>
                            {{ form.email_host }}
                            {% if form.email_host.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email_host.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.email_port.id_for_label }}" class="form-label">
                                <i class="fas fa-network-wired me-1"></i>Porta
                            </label>
                            {{ form.email_port }}
                            {% if form.email_port.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email_port.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.email_host_user.id_for_label }}" class="form-label">
                                <i class="fas fa-user me-1"></i>Usuário
                            </label>
                            {{ form.email_host_user }}
                            {% if form.email_host_user.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email_host_user.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.email_host_password.id_for_label }}" class="form-label">
                                <i class="fas fa-lock me-1"></i>Senha
                            </label>
                            <div class="input-group">
                                {{ form.email_host_password }}
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            {% if form.email_host_password.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email_host_password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.email_use_tls.id_for_label }}" class="form-label">
                                <i class="fas fa-shield-alt me-1"></i>Usar TLS
                            </label>
                            <div class="form-check form-switch">
                                {{ form.email_use_tls }}
                                <label class="form-check-label" for="{{ form.email_use_tls.id_for_label }}">
                                    Ativar conexão segura (TLS)
                                </label>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.email_use_ssl.id_for_label }}" class="form-label">
                                <i class="fas fa-lock me-1"></i>Usar SSL
                            </label>
                            <div class="form-check form-switch">
                                {{ form.email_use_ssl }}
                                <label class="form-check-label" for="{{ form.email_use_ssl.id_for_label }}">
                                    Ativar conexão SSL
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.default_from_email.id_for_label }}" class="form-label">
                                <i class="fas fa-at me-1"></i>Email Padrão
                            </label>
                            {{ form.default_from_email }}
                            {% if form.default_from_email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.default_from_email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.server_email.id_for_label }}" class="form-label">
                                <i class="fas fa-server me-1"></i>Email do Servidor
                            </label>
                            {{ form.server_email }}
                            {% if form.server_email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.server_email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.email_timeout.id_for_label }}" class="form-label">
                                <i class="fas fa-clock me-1"></i>Timeout (segundos)
                            </label>
                            {{ form.email_timeout }}
                            {% if form.email_timeout.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email_timeout.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Tempo limite para conexão SMTP. Ex: 30</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="alert alert-secondary small mb-0">
                                <strong>Exemplo Gmail:</strong><br>
                                Servidor: <code>smtp.gmail.com</code><br>
                                Porta: <code>587</code> (TLS)<br>
                                Usuário: <code><EMAIL></code><br>
                                Senha: <code>senha de app</code><br>
                                TLS: <code>Ativado</code><br>
                                SSL: <code>Desativado</code>
                            </div>
                        </div>
                    </div>

                    <!-- Alerta sobre persistência -->
                    <div class="alert alert-info mb-3">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-info-circle me-2 mt-1"></i>
                            <div>
                                <strong>Importante:</strong> As configurações são salvas e aplicadas imediatamente, mas para garantir persistência completa após reinicializar o servidor, reinicie o Django após salvar.
                                <br><small class="text-muted">Isso garante que as configurações sejam carregadas corretamente do arquivo .env.</small>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Salvar Configurações
                        </button>
                        <div>
                            <a href="{% url 'config:email_test' %}" class="btn btn-outline-info me-2">
                                <i class="fas fa-paper-plane me-2"></i>Testar Configuração
                            </a>
                            <button type="button" class="btn btn-outline-warning" onclick="syncEmailConfig()">
                                <i class="fas fa-sync me-2"></i>Sincronizar
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Sidebar com ações rápidas -->
    <div class="col-12 col-lg-4">
        <!-- Configurações Rápidas -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>Configurações Rápidas
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-success" onclick="quickSetup('console')">
                        <i class="fas fa-terminal me-2"></i>Modo Console (Dev)
                    </button>
                    <button type="button" class="btn btn-outline-primary" onclick="quickSetup('file')">
                        <i class="fas fa-file me-2"></i>Modo Arquivo
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="quickSetup('dummy')">
                        <i class="fas fa-ban me-2"></i>Desabilitar Email
                    </button>
                </div>
                
                <hr>
                
                <h6 class="mb-3">Configurar Gmail:</h6>
                <div class="mb-3">
                    <input type="email" class="form-control mb-2" id="gmailUser" placeholder="<EMAIL>">
                    <input type="password" class="form-control mb-2" id="gmailPassword" placeholder="Senha de app">
                    <button type="button" class="btn btn-outline-info w-100" onclick="setupGmail()">
                        <i class="fab fa-google me-2"></i>Configurar Gmail
                    </button>
                </div>
            </div>
        </div>

        <!-- Informações do Sistema -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light">
                <h6 class="card-title mb-0">
                    <i class="fas fa-cogs me-2"></i>Informações do Sistema
                </h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <div class="mb-2">
                        <strong>Backend Atual:</strong>
                        <br><code>{{ current_backend }}</code>
                    </div>
                    
                    <div class="mb-2">
                        <strong>Configuração:</strong>
                        <br>
                        {% if email_configured %}
                            <span class="badge bg-success">Ativa</span>
                        {% else %}
                            <span class="badge bg-warning">Pendente</span>
                        {% endif %}
                    </div>
                    
                    <div class="mb-2">
                        <strong>Persistência:</strong>
                        <br>
                        <span class="badge bg-info">Banco + .env</span>
                    </div>
                    
                    <div class="mb-2">
                        <strong>Última Atualização:</strong>
                        <br><small class="text-muted">Agora</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ações de Sistema -->
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h6 class="card-title mb-0">
                    <i class="fas fa-tools me-2"></i>Ações de Sistema
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="testConnection()">
                        <i class="fas fa-plug me-2"></i>Testar Conexão
                    </button>
                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="syncConfig()">
                        <i class="fas fa-sync me-2"></i>Sincronizar Config
                    </button>
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="showDetailedStatus()">
                        <i class="fas fa-list me-2"></i>Status Detalhado
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para Status Detalhado -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle me-2"></i>Status Detalhado do Email
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="statusModalBody">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Carregando...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Função para sincronizar configurações
function syncEmailConfig() {
    if (confirm('Deseja sincronizar as configurações de email do banco para o arquivo .env? Isso garantirá persistência após reinicializar o servidor.')) {
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sincronizando...';
        btn.disabled = true;
        
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value || 
                         document.querySelector('meta[name=csrf-token]')?.getAttribute('content') ||
                         '{{ csrf_token }}';
        
        fetch('{% url "config:email_sync" %}', {
            method: 'POST',
            headers: {
                'X-CSRFToken': csrfToken,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ Configurações sincronizadas com sucesso!\n\nPara aplicar completamente, reinicie o servidor Django.');
                location.reload();
            } else {
                alert('❌ Erro ao sincronizar: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('❌ Erro inesperado ao sincronizar configurações');
        })
        .finally(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
    }
}

// Configuração rápida
function quickSetup(backend) {
    const configs = {
        'console': {
            title: 'Modo Console (Desenvolvimento)',
            description: 'Emails aparecem no terminal - ideal para desenvolvimento',
            backend: 'django.core.mail.backends.console.EmailBackend'
        },
        'file': {
            title: 'Modo Arquivo',
            description: 'Emails são salvos em arquivos - útil para debug',
            backend: 'django.core.mail.backends.filebased.EmailBackend'
        },
        'dummy': {
            title: 'Email Desabilitado',
            description: 'Sistema de email desabilitado - nenhum email será enviado',
            backend: 'django.core.mail.backends.dummy.EmailBackend'
        }
    };
    
    const config = configs[backend];
    
    if (confirm(`Configurar para ${config.title}?\n\n${config.description}`)) {
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Configurando...';
        btn.disabled = true;
        
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value || 
                         document.querySelector('meta[name=csrf-token]')?.getAttribute('content') ||
                         '{{ csrf_token }}';
        
        fetch('{% url "config:email_quick_setup" %}', {
            method: 'POST',
            headers: {
                'X-CSRFToken': csrfToken,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                backend: backend
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ Configuração aplicada com sucesso!\n\n' + data.message);
                location.reload();
            } else {
                alert('❌ Erro: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('❌ Erro inesperado ao configurar');
        })
        .finally(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
    }
}

// Configurar Gmail
function setupGmail() {
    const user = document.getElementById('gmailUser').value;
    const password = document.getElementById('gmailPassword').value;
    
    if (!user || !password) {
        alert('Por favor, preencha o email e senha do Gmail');
        return;
    }
    
    if (confirm('Configurar email Gmail?\n\nCertifique-se de usar uma senha de app, não sua senha normal do Gmail.')) {
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Configurando...';
        btn.disabled = true;
        
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value || 
                         document.querySelector('meta[name=csrf-token]')?.getAttribute('content') ||
                         '{{ csrf_token }}';
        
        fetch('{% url "config:email_quick_setup" %}', {
            method: 'POST',
            headers: {
                'X-CSRFToken': csrfToken,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                backend: 'smtp',
                host: 'smtp.gmail.com',
                user: user,
                password: password,
                from_email: user,
                tls: true
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ Gmail configurado com sucesso!\n\n' + data.message);
                location.reload();
            } else {
                alert('❌ Erro: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('❌ Erro inesperado ao configurar Gmail');
        })
        .finally(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
    }
}

// Testar conexão
function testConnection() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Testando...';
    btn.disabled = true;
    
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value || 
                     document.querySelector('meta[name=csrf-token]')?.getAttribute('content') ||
                     '{{ csrf_token }}';
    
    fetch('{% url "config:test_email_connection" %}', {
        method: 'POST',
        headers: {
            'X-CSRFToken': csrfToken,
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ ' + data.message);
        } else {
            alert('❌ ' + data.message);
        }
    })
    .catch(error => {
        console.error('Erro:', error);
        alert('❌ Erro inesperado ao testar conexão');
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

// Sincronizar configuração
function syncConfig() {
    syncEmailConfig();
}

// Mostrar status detalhado
function showDetailedStatus() {
    const modal = new bootstrap.Modal(document.getElementById('statusModal'));
    modal.show();
    
    const modalBody = document.getElementById('statusModalBody');
    modalBody.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p class="mt-2">Carregando status detalhado...</p></div>';
    
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value || 
                     document.querySelector('meta[name=csrf-token]')?.getAttribute('content') ||
                     '{{ csrf_token }}';
    
    fetch('{% url "config:email_detailed_status" %}', {
        method: 'POST',
        headers: {
            'X-CSRFToken': csrfToken,
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            modalBody.innerHTML = data.html;
        } else {
            modalBody.innerHTML = '<div class="alert alert-danger">Erro ao carregar status: ' + data.message + '</div>';
        }
    })
    .catch(error => {
        console.error('Erro:', error);
        modalBody.innerHTML = '<div class="alert alert-danger">Erro inesperado ao carregar status</div>';
    });
}

// Toggle password visibility
document.getElementById('togglePassword')?.addEventListener('click', function() {
    const passwordField = document.querySelector('#{{ form.email_host_password.id_for_label }}');
    const icon = this.querySelector('i');
    
    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
});
</script>
{% endblock %}

# Generated by Django 5.2.2 on 2025-07-22 21:00

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Book',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='Título')),
                ('author', models.CharField(blank=True, max_length=120, verbose_name='Autor')),
                ('description', models.TextField(blank=True, verbose_name='Descrição')),
                ('published_date', models.DateField(blank=True, null=True, verbose_name='Data de Publicação')),
                ('cover_image', models.ImageField(blank=True, null=True, upload_to='books/covers/', verbose_name='Capa')),
                ('file', models.FileField(blank=True, null=True, upload_to='books/ebooks/', verbose_name='Arquivo')),
                ('slug', models.SlugField(blank=True, unique=True, verbose_name='Slug')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Atualizado em')),
            ],
            options={
                'verbose_name': 'Livro',
                'verbose_name_plural': 'Livros',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='BookFavorite',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Adicionado em')),
                ('book', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='books.book')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Livro Favorito',
                'verbose_name_plural': 'Livros Favoritos',
                'unique_together': {('user', 'book')},
            },
        ),
        migrations.CreateModel(
            name='BookProgress',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('location', models.CharField(max_length=255, verbose_name='Localização EPUB/PDF')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Atualizado em')),
                ('book', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='books.book')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Progresso de Leitura',
                'verbose_name_plural': 'Progressos de Leitura',
                'unique_together': {('user', 'book')},
            },
        ),
    ]

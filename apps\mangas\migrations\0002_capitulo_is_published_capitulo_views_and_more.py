# Generated by Django 5.2.4 on 2025-07-28 03:16

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mangas', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='capitulo',
            name='is_published',
            field=models.BooleanField(default=True, help_text='Se o capítulo está visível publicamente', verbose_name='Publicado?'),
        ),
        migrations.AddField(
            model_name='capitulo',
            name='views',
            field=models.PositiveIntegerField(default=0, help_text='Número de visualizações do capítulo', verbose_name='Visualizações'),
        ),
        migrations.AddField(
            model_name='manga',
            name='is_published',
            field=models.BooleanField(default=True, help_text='Se o mangá está visível publicamente', verbose_name='Publicado?'),
        ),
        migrations.AddField(
            model_name='pagina',
            name='content_type',
            field=models.CharField(blank=True, help_text='Tipo MIME da imagem', max_length=100, verbose_name='Tipo de conteúdo'),
        ),
        migrations.AddField(
            model_name='pagina',
            name='file_size',
            field=models.PositiveIntegerField(blank=True, help_text='Tamanho do arquivo em bytes', null=True, verbose_name='Tamanho do arquivo'),
        ),
        migrations.AddField(
            model_name='pagina',
            name='height',
            field=models.PositiveIntegerField(blank=True, help_text='Altura da imagem em pixels', null=True, verbose_name='Altura'),
        ),
        migrations.AddField(
            model_name='pagina',
            name='width',
            field=models.PositiveIntegerField(blank=True, help_text='Largura da imagem em pixels', null=True, verbose_name='Largura'),
        ),
        migrations.AlterField(
            model_name='capitulo',
            name='manga',
            field=models.ForeignKey(help_text='Mangá ao qual este capítulo pertence', on_delete=django.db.models.deletion.CASCADE, related_name='capitulos', to='mangas.manga', verbose_name='Mangá'),
        ),
        migrations.AlterField(
            model_name='capitulo',
            name='number',
            field=models.PositiveIntegerField(help_text='Número sequencial do capítulo', verbose_name='Número do Capítulo'),
        ),
        migrations.AlterField(
            model_name='capitulo',
            name='slug',
            field=models.SlugField(blank=True, help_text='Identificador único para URLs', max_length=255, verbose_name='Slug'),
        ),
        migrations.AlterField(
            model_name='capitulo',
            name='title',
            field=models.CharField(blank=True, help_text='Título opcional do capítulo', max_length=200, verbose_name='Título'),
        ),
        migrations.AlterField(
            model_name='manga',
            name='author',
            field=models.CharField(blank=True, help_text='Autor do mangá', max_length=120, verbose_name='Autor'),
        ),
        migrations.AlterField(
            model_name='manga',
            name='cover_image',
            field=models.ImageField(blank=True, help_text='Capa do mangá', null=True, upload_to='mangas/covers/%Y/%m/%d/', verbose_name='Capa'),
        ),
        migrations.AlterField(
            model_name='manga',
            name='description',
            field=models.TextField(blank=True, help_text='Sinopse ou descrição do mangá', verbose_name='Descrição'),
        ),
        migrations.AlterField(
            model_name='manga',
            name='slug',
            field=models.SlugField(blank=True, max_length=255, unique=True, verbose_name='Slug'),
        ),
        migrations.AlterField(
            model_name='manga',
            name='title',
            field=models.CharField(help_text='Título do mangá', max_length=200, verbose_name='Título'),
        ),
        migrations.AlterField(
            model_name='pagina',
            name='capitulo',
            field=models.ForeignKey(help_text='Capítulo ao qual esta página pertence', on_delete=django.db.models.deletion.CASCADE, related_name='paginas', to='mangas.capitulo', verbose_name='Capítulo'),
        ),
        migrations.AlterField(
            model_name='pagina',
            name='image',
            field=models.ImageField(help_text='Imagem da página', upload_to='mangas/pages/%Y/%m/%d/', verbose_name='Imagem'),
        ),
        migrations.AlterField(
            model_name='pagina',
            name='number',
            field=models.PositiveIntegerField(help_text='Número sequencial da página no capítulo', verbose_name='Número da Página'),
        ),
        migrations.AddIndex(
            model_name='capitulo',
            index=models.Index(fields=['slug'], name='chapter_slug_idx'),
        ),
        migrations.AddIndex(
            model_name='capitulo',
            index=models.Index(fields=['number'], name='chapter_number_idx'),
        ),
        migrations.AddIndex(
            model_name='capitulo',
            index=models.Index(fields=['is_published'], name='chapter_published_idx'),
        ),
        migrations.AddIndex(
            model_name='manga',
            index=models.Index(fields=['slug'], name='manga_slug_idx'),
        ),
        migrations.AddIndex(
            model_name='manga',
            index=models.Index(fields=['created_at'], name='manga_created_at_idx'),
        ),
        migrations.AddIndex(
            model_name='manga',
            index=models.Index(fields=['is_published'], name='manga_published_idx'),
        ),
        migrations.AddIndex(
            model_name='pagina',
            index=models.Index(fields=['number'], name='page_number_idx'),
        ),
    ]

# Articles

## Responsabilidade
Gerencia artigos, categorias, comentários e tags do sistema de conteúdo.

## Estrutura
- `models/`: Modelos de artigo, categoria, comentário, tag.
- `views/`: Views para listagem, detalhe, comentários.
- `forms/`: Formulários de artigo e comentário.
- `services/`: Lógica de artigos e comentários.
- `repositories/`: Acesso a dados de artigos.
- `templates/`: Templates HTML de artigos e comentários.

## Integração
- Relaciona usuários da app `accounts` como autores/comentaristas.
- Usa tags e categorias para navegação e SEO.

## Pontos de atenção
- Moderação de comentários.
- SEO e performance de listagens. 
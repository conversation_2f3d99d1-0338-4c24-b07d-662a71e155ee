{% extends 'base.html' %}
{% load static %}

{% block title %}Audiolivros - Project Nix{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h2">
                    <i class="fas fa-headphones me-2"></i>
                    Audiolivros
                </h1>
                {% if user.is_staff %}
                <a href="{% url 'audiobooks:audiobook_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Adicionar Audiolivro
                </a>
                {% endif %}
            </div>

            <!-- Search Bar -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <form method="get" class="d-flex">
                        <input type="search" name="q" class="form-control me-2" 
                               placeholder="Buscar audiolivros..." value="{{ request.GET.q }}">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>
            </div>

            <!-- Audiobooks Grid -->
            {% if audiobooks %}
            <div class="row">
                {% for audiobook in audiobooks %}
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                    <div class="card h-100 shadow-sm">
                        {% if audiobook.cover_image %}
                        <img src="{{ audiobook.cover_image.url }}" class="card-img-top" 
                             alt="{{ audiobook.title }}" style="height: 250px; object-fit: cover;">
                        {% else %}
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                             style="height: 250px;">
                            <i class="fas fa-headphones fa-3x text-muted"></i>
                        </div>
                        {% endif %}
                        
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title">{{ audiobook.title }}</h5>
                            {% if audiobook.author %}
                            <p class="card-text text-muted mb-1">
                                <i class="fas fa-user me-1"></i>{{ audiobook.author }}
                            </p>
                            {% endif %}
                            {% if audiobook.narrator %}
                            <p class="card-text text-muted mb-2">
                                <i class="fas fa-microphone me-1"></i>{{ audiobook.narrator }}
                            </p>
                            {% endif %}
                            {% if audiobook.description %}
                            <p class="card-text flex-grow-1">
                                {{ audiobook.description|truncatewords:15 }}
                            </p>
                            {% endif %}
                            {% if audiobook.duration %}
                            <p class="card-text text-muted mb-2">
                                <i class="fas fa-clock me-1"></i>{{ audiobook.duration }}
                            </p>
                            {% endif %}
                            <div class="mt-auto">
                                <a href="{{ audiobook.get_absolute_url }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-play me-1"></i>Ouvir
                                </a>
                                {% if user.is_staff %}
                                <a href="{% url 'audiobooks:audiobook_edit' audiobook.slug %}" 
                                   class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- Pagination -->
            {% if is_paginated %}
            <nav aria-label="Navegação de páginas">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}">
                            Primeira
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}">
                            Anterior
                        </a>
                    </li>
                    {% endif %}

                    <li class="page-item active">
                        <span class="page-link">
                            Página {{ page_obj.number }} de {{ page_obj.paginator.num_pages }}
                        </span>
                    </li>

                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}">
                            Próxima
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}">
                            Última
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}

            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-headphones fa-4x text-muted mb-3"></i>
                <h3 class="text-muted">Nenhum audiolivro encontrado</h3>
                {% if request.GET.q %}
                <p class="text-muted">Tente uma busca diferente ou 
                    <a href="{% url 'audiobooks:audiobook_list' %}">veja todos os audiolivros</a>
                </p>
                {% else %}
                <p class="text-muted">Seja o primeiro a adicionar um audiolivro!</p>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

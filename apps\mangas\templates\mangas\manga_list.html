{% extends 'base.html' %}
{% load static %}

{% block title %}Mangás - Project Nix{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h2 mb-0 text-sans text-body">
                    <i class="fas fa-book-open me-2 text-theme-primary"></i>Mangás
                </h1>
                {% if user.is_authenticated and user.is_staff %}
                <a href="{% url 'mangas:manga_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Novo Mangá
                </a>
                {% endif %}
            </div>
            <form class="mb-4" method="get">
                <div class="input-group">
                    <input type="text" name="q" class="form-control" placeholder="Buscar mangá..." value="{{ request.GET.q }}">
                    <button class="btn btn-outline-primary" type="submit"><i class="fas fa-search"></i></button>
                </div>
            </form>
            {% if mangas %}
                <div class="row g-4">
                    {% for manga in mangas %}
                    <div class="col-md-6 col-lg-4">
                        {% include 'includes/content_card.html' with 
                            image_url=manga.cover_image.url if manga.cover_image else None
                            image_alt=manga.title
                            detail_url=manga.get_absolute_url
                            title=manga.title
                            subtitle=None
                            author=manga.author
                            date=manga.created_at|date:'d M, Y'
                            description=manga.description
                            primary_action_url=manga.get_absolute_url
                            primary_action_label='Ver detalhes'
                        %}
                    </div>
                    {% endfor %}
                </div>
                {% if is_paginated %}
                <nav aria-label="Paginação" class="mt-5">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Anterior</a>
                            </li>
                        {% endif %}
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}">Próximo</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-book-open fa-5x text-theme-secondary mb-3"></i>
                    <h3 class="text-theme-secondary text-sans text-body">Nenhum mangá encontrado</h3>
                    <p class="text-theme-secondary text-body">Ainda não há mangás cadastrados.</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %} 
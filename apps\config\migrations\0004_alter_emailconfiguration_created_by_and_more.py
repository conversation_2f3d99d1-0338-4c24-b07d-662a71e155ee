# Generated by Django 5.2.2 on 2025-06-06 20:33

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('config', '0003_alter_emailconfiguration_email_host_password_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name='emailconfiguration',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='email_configs_created', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='emailconfiguration',
            name='updated_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='email_configs_updated', to=settings.AUTH_USER_MODEL),
        ),
    ]

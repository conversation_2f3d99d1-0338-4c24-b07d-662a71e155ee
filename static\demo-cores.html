<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demonstração da Nova Paleta Roxo Nix</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="css/main.css" rel="stylesheet">
    <link href="css/accessibility.css" rel="stylesheet">
    <style>
        .color-card {
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border: 1px solid var(--border-color);
            transition: var(--transition-normal);
        }
        .color-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }
        .color-swatch {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            border: 2px solid var(--border-color);
            margin-right: 1rem;
        }
        .color-info {
            font-family: var(--font-family-mono);
            font-size: 0.875rem;
        }
        .demo-button {
            margin: 0.5rem;
        }
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="theme-toggle">
        <button class="btn btn-outline-secondary" onclick="toggleTheme()">
            <i class="fas fa-moon" id="theme-icon"></i>
        </button>
    </div>

    <div class="container py-5">
        <div class="text-center mb-5">
            <h1 class="display-4 fw-bold" style="color: var(--nix-accent);">Nova Paleta Roxo Nix</h1>
            <p class="lead">Sistema de cores elegante e acessível</p>
        </div>

        <!-- Cores Primárias -->
        <section class="mb-5">
            <h2 class="h3 mb-4">🎨 Cores Primárias</h2>
            <div class="row">
                <div class="col-md-4">
                    <div class="color-card">
                        <div class="d-flex align-items-center">
                            <div class="color-swatch" style="background-color: var(--nix-accent);"></div>
                            <div>
                                <h5>Roxo Elegante</h5>
                                <div class="color-info">--nix-accent<br>#7c3aed</div>
                                <small class="text-muted">Contraste: 5.1:1</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="color-card">
                        <div class="d-flex align-items-center">
                            <div class="color-swatch" style="background-color: var(--nix-accent-light);"></div>
                            <div>
                                <h5>Roxo Claro</h5>
                                <div class="color-info">--nix-accent-light<br>#8b5cf6</div>
                                <small class="text-muted">Contraste: 4.6:1</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="color-card">
                        <div class="d-flex align-items-center">
                            <div class="color-swatch" style="background-color: var(--nix-accent-dark);"></div>
                            <div>
                                <h5>Roxo Escuro</h5>
                                <div class="color-info">--nix-accent-dark<br>#5b21b6</div>
                                <small class="text-muted">Contraste: 6.8:1</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Cores Complementares -->
        <section class="mb-5">
            <h2 class="h3 mb-4">🌈 Cores Complementares</h2>
            <div class="row">
                <div class="col-md-6">
                    <div class="color-card">
                        <div class="d-flex align-items-center">
                            <div class="color-swatch" style="background-color: var(--nix-accent-alt);"></div>
                            <div>
                                <h5>Índigo Complementar</h5>
                                <div class="color-info">--nix-accent-alt<br>#6366f1</div>
                                <small class="text-muted">Contraste: 4.9:1</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="color-card">
                        <div class="d-flex align-items-center">
                            <div class="color-swatch" style="background-color: var(--nix-primary);"></div>
                            <div>
                                <h5>Azul Nix</h5>
                                <div class="color-info">--nix-primary<br>#1a1d29</div>
                                <small class="text-muted">Contraste: 13.5:1</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Demonstração de Componentes -->
        <section class="mb-5">
            <h2 class="h3 mb-4">🧩 Componentes</h2>
            
            <!-- Botões -->
            <div class="mb-4">
                <h4>Botões</h4>
                <button class="btn btn-primary demo-button">Botão Primário</button>
                <button class="btn btn-outline-primary demo-button">Botão Outline</button>
                <button class="btn btn-secondary demo-button">Botão Secundário</button>
            </div>

            <!-- Links -->
            <div class="mb-4">
                <h4>Links</h4>
                <p>
                    <a href="#" class="me-3">Link Normal</a>
                    <a href="#" class="me-3" style="color: var(--link-visited-color);">Link Visitado</a>
                    <a href="#" class="me-3" onmouseover="this.style.color='var(--link-hover-color)'" onmouseout="this.style.color='var(--link-color)'">Link Hover</a>
                </p>
            </div>

            <!-- Formulários -->
            <div class="mb-4">
                <h4>Formulários</h4>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Campo de Texto</label>
                            <input type="text" class="form-control" placeholder="Digite algo...">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Select</label>
                            <select class="form-select">
                                <option>Opção 1</option>
                                <option>Opção 2</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cards -->
            <div class="mb-4">
                <h4>Cards</h4>
                <div class="row">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title" style="color: var(--nix-accent);">Card Exemplo</h5>
                                <p class="card-text">Este é um exemplo de card com a nova paleta de cores.</p>
                                <a href="#" class="btn btn-primary">Ver Mais</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Acessibilidade -->
        <section class="mb-5">
            <h2 class="h3 mb-4">♿ Acessibilidade</h2>
            <div class="alert alert-success">
                <h5>✅ Conformidade WCAG 2.1 AA</h5>
                <ul class="mb-0">
                    <li>Todos os contrastes ≥ 4.5:1 para texto normal</li>
                    <li>Contrastes ≥ 3:1 para elementos não-textuais</li>
                    <li>Suporte completo a navegação por teclado</li>
                    <li>Compatível com leitores de tela</li>
                </ul>
            </div>
        </section>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/js/all.min.js"></script>
    <script>
        function toggleTheme() {
            const html = document.documentElement;
            const icon = document.getElementById('theme-icon');
            const currentTheme = html.getAttribute('data-theme');
            
            if (currentTheme === 'dark') {
                html.setAttribute('data-theme', 'light');
                icon.className = 'fas fa-moon';
            } else {
                html.setAttribute('data-theme', 'dark');
                icon.className = 'fas fa-sun';
            }
        }

        // Aplicar tema inicial
        document.documentElement.setAttribute('data-theme', 'light');
    </script>
</body>
</html>

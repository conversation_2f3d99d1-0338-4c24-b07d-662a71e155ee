/* 
 * CSS específico para listagem de artigos
 * Separado seguindo princípios de organização
 */

/* Article Cards */
.article-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border: none;
    border-radius: 12px;
    overflow: hidden;
}

.article-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.article-card .card-title a:hover {
    color: var(--bs-primary) !important;
}

.article-stats small {
    font-size: 0.8rem;
}

.article-actions .btn {
    border-radius: 20px;
}

.badge {
    border-radius: 20px;
    font-size: 0.7rem;
    font-weight: 500;
}

/* Search and Filters */
.search-filters {
    background-color: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
}

.search-form {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.search-form .form-control {
    flex: 1;
}

.filter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.filter-tag {
    background-color: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    text-decoration: none;
    transition: background-color 0.2s;
}

.filter-tag:hover,
.filter-tag.active {
    background-color: #007bff;
    color: white;
}

/* Category Sidebar */
.category-sidebar {
    background-color: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    height: fit-content;
}

.category-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.category-list li {
    margin-bottom: 0.5rem;
}

.category-list a {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 0.5rem;
    color: #495057;
    text-decoration: none;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.category-list a:hover,
.category-list a.active {
    background-color: #007bff;
    color: white;
}

.category-count {
    background-color: #e9ecef;
    color: #495057;
    padding: 0.125rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
}

.category-list a:hover .category-count,
.category-list a.active .category-count {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
}

/* Pagination */
.pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 3rem;
}

.pagination .page-link {
    border-radius: 8px;
    margin: 0 0.125rem;
    border: 1px solid #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
}

/* Featured Articles */
.featured-articles {
    margin-bottom: 3rem;
}

.featured-article {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    height: 300px;
    background: linear-gradient(45deg, #007bff, #6610f2);
}

.featured-article .card-img {
    height: 100%;
    object-fit: cover;
}

.featured-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 2rem;
}

.featured-overlay h3 {
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.featured-overlay p {
    margin-bottom: 1rem;
    opacity: 0.9;
}

/* Article Grid */
.articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.skeleton-card {
    height: 300px;
    border-radius: 12px;
}

.skeleton-text {
    height: 1rem;
    border-radius: 4px;
    margin-bottom: 0.5rem;
}

.skeleton-text.short {
    width: 60%;
}

.skeleton-text.medium {
    width: 80%;
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h3 {
    margin-bottom: 1rem;
    color: #495057;
}

.empty-state p {
    margin-bottom: 2rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
    .article-card .card-body {
        padding: 1rem;
    }
    
    .article-stats {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .search-form {
        flex-direction: column;
    }
    
    .search-form .btn {
        width: 100%;
    }
    
    .articles-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .featured-overlay {
        padding: 1rem;
    }
    
    .category-sidebar {
        margin-bottom: 2rem;
    }
}

@media (max-width: 576px) {
    .filter-tags {
        justify-content: center;
    }
    
    .pagination-wrapper {
        margin-top: 2rem;
    }
    
    .pagination .page-link {
        padding: 0.375rem 0.5rem;
        font-size: 0.875rem;
    }
}

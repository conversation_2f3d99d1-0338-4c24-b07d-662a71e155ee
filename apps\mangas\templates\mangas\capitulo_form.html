{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}{% if form.instance.pk %}Editar{% else %}Adicionar{% endif %} Capítulo - Project Nix{% endblock %}

{% block extra_css %}
<style>
    .file-upload-container {
        border: 2px dashed #dee2e6;
        border-radius: 5px;
        padding: 2rem;
        text-align: center;
        margin-bottom: 1.5rem;
        background-color: #f8f9fa;
        transition: all 0.3s ease;
    }
    
    .file-upload-container:hover {
        border-color: #0d6efd;
        background-color: #f0f7ff;
    }
    
    .file-upload-container.dragover {
        border-color: #0d6efd;
        background-color: #e7f1ff;
    }
    
    .file-upload-icon {
        font-size: 3rem;
        color: #6c757d;
        margin-bottom: 1rem;
    }
    
    .file-info {
        margin-top: 1rem;
        font-size: 0.9rem;
        color: #6c757d;
    }
    
    .file-preview {
        margin-top: 1rem;
        max-height: 200px;
        overflow-y: auto;
        text-align: left;
    }
    
    .file-item {
        padding: 0.5rem;
        border-bottom: 1px solid #eee;
        font-size: 0.85rem;
    }
    
    .file-item:last-child {
        border-bottom: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm border-0">
                <div class="card-body">
                    <h1 class="h4 mb-4">
                        {% if form.instance.pk %}
                            Editar Capítulo
                        {% else %}
                            Adicionar Novo Capítulo
                        {% endif %}
                    </h1>
                    
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                    
                    <form method="post" enctype="multipart/form-data" id="chapterForm" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.number.id_for_label }}" class="form-label">Número do Capítulo</label>
                            <input type="number" 
                                   name="{{ form.number.name }}" 
                                   class="form-control {% if form.number.errors %}is-invalid{% endif %}" 
                                   id="{{ form.number.id_for_label }}" 
                                   value="{{ form.number.value|default:'' }}" 
                                   required>
                            {% if form.number.help_text %}
                                <div class="form-text">{{ form.number.help_text }}</div>
                            {% endif %}
                            {% if form.number.errors %}
                                <div class="invalid-feedback">
                                    {{ form.number.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.title.id_for_label }}" class="form-label">Título do Capítulo (opcional)</label>
                            <input type="text" 
                                   name="{{ form.title.name }}" 
                                   class="form-control {% if form.title.errors %}is-invalid{% endif %}" 
                                   id="{{ form.title.id_for_label }}" 
                                   value="{{ form.title.value|default:'' }}">
                            {% if form.title.help_text %}
                                <div class="form-text">{{ form.title.help_text }}</div>
                            {% endif %}
                            {% if form.title.errors %}
                                <div class="invalid-feedback">
                                    {{ form.title.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Arquivo do Capítulo</label>
                            <div class="file-upload-container {% if form.arquivo_capitulo.errors %}border-danger{% endif %}" id="dropZone">
                                <div class="file-upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <p>Arraste e solte arquivos aqui ou clique para selecionar</p>
                                <p class="text-muted small">
                                    Suporta arquivos .zip, .rar, .cbz, .cbr ou pastas com imagens
                                </p>
                                <input type="file" 
                                       name="{{ form.arquivo_capitulo.name }}" 
                                       id="{{ form.arquivo_capitulo.id_for_label }}" 
                                       {% if not form.instance.pk %}required{% endif %}
                                       style="display: none;"
                                       multiple>
                                <div class="file-info" id="fileInfo">Nenhum arquivo selecionado</div>
                                <div class="file-preview" id="filePreview"></div>
                                <!-- Barra de progresso -->
                                <div class="progress mt-3" style="height: 22px; display: none;" id="uploadProgressBarContainer">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" id="uploadProgressBar" role="progressbar" style="width: 0%">0%</div>
                                </div>
                                <div class="alert alert-danger mt-2 d-none" id="uploadError"></div>
                                <div class="alert alert-warning mt-2 d-none" id="uploadWarning"></div>
                                {% if form.arquivo_capitulo.help_text %}
                                    <div class="form-text">{{ form.arquivo_capitulo.help_text }}</div>
                                {% endif %}
                                {% if form.arquivo_capitulo.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.arquivo_capitulo.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end gap-2 mt-4">
                            <a href="{{ request.META.HTTP_REFERER|default:'' }}{% if not request.META.HTTP_REFERER %}{% url 'mangas:manga_detail' slug=view.kwargs.manga_slug %}{% endif %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i> Cancelar
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Salvar Capítulo
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const dropZone = document.getElementById('dropZone');
        const fileInput = document.querySelector('input[type="file"]');
        const fileInfo = document.getElementById('fileInfo');
        const filePreview = document.getElementById('filePreview');
        const form = document.getElementById('chapterForm');
        const submitBtn = form.querySelector('button[type="submit"]');
        const progressBarContainer = document.getElementById('uploadProgressBarContainer');
        const progressBar = document.getElementById('uploadProgressBar');
        const uploadError = document.getElementById('uploadError');
        const uploadWarning = document.getElementById('uploadWarning');
        const MAX_PAGES = {{ MAX_PAGES_PER_CHAPTER|default:200 }};
        const ALLOWED_EXTS = ['.zip','.rar','.cbz','.cbr','.7z','.tar','.tar.gz','.tar.bz2','.tar.xz','.cb7','.cbt','.cba','.jpg','.jpeg','.png','.webp','.gif','.bmp','.tiff','.tif'];

        // Estilizar o input de arquivo para ficar invisível
        fileInput.style.display = 'none';
        
        // Eventos para a área de drop
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, preventDefaults, false);
        });
        
        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }
        
        // Efeito visual ao arrastar arquivos
        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, highlight, false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, unhighlight, false);
        });
        
        function highlight() {
            dropZone.classList.add('dragover');
        }
        
        function unhighlight() {
            dropZone.classList.remove('dragover');
        }
        
        // Manipular o drop de arquivos
        dropZone.addEventListener('drop', handleDrop, false);
        
        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            handleFiles(files);
        }
        
        // Manipular clique na área de drop
        dropZone.addEventListener('click', () => {
            fileInput.click();
        });
        
        // Manipular seleção de arquivos
        fileInput.addEventListener('change', function() {
            handleFiles(this.files);
        });
        
        // Processar os arquivos selecionados
        function handleFiles(files) {
            uploadError.classList.add('d-none');
            uploadWarning.classList.add('d-none');
            // Atualizar o input de arquivo para refletir os arquivos selecionados
            const dataTransfer = new DataTransfer();
            // Limpar a visualização anterior
            filePreview.innerHTML = '';
            let invalid = Array.from(files).filter(f => !ALLOWED_EXTS.some(ext => f.name.toLowerCase().endsWith(ext)));
            if (invalid.length > 0) {
                uploadError.textContent = 'Alguns arquivos possuem extensão não permitida: ' + invalid.map(f => f.name).join(', ');
                uploadError.classList.remove('d-none');
                fileInput.value = '';
                fileInfo.textContent = 'Nenhum arquivo selecionado';
                return;
            }
            if (files.length > MAX_PAGES) {
                uploadWarning.textContent = `Você selecionou ${files.length} arquivos. O limite por capítulo é ${MAX_PAGES}. Apenas os primeiros ${MAX_PAGES} serão enviados.`;
                uploadWarning.classList.remove('d-none');
            }
            // Adicionar cada arquivo ao DataTransfer e à visualização
            Array.from(files).forEach(file => {
                dataTransfer.items.add(file);
                // Adicionar à visualização
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <i class="fas fa-file-image me-2"></i>
                    ${file.name} (${formatFileSize(file.size)})
                `;
                filePreview.appendChild(fileItem);
            });
            // Atualizar o input de arquivo
            fileInput.files = dataTransfer.files;
            // Atualizar informações do arquivo
            if (files.length > 0) {
                fileInfo.textContent = `${files.length} arquivo(s) selecionado(s)`;
            } else {
                fileInfo.textContent = 'Nenhum arquivo selecionado';
            }
        }
        // Função auxiliar para formatar o tamanho do arquivo
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        // Barra de progresso real no submit
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            uploadError.classList.add('d-none');
            progressBarContainer.style.display = 'block';
            progressBar.style.width = '0%';
            progressBar.textContent = '0%';
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Enviando...';
            let formData = new FormData(form);
            let xhr = new XMLHttpRequest();
            xhr.open('POST', window.location.href, true);
            xhr.upload.onprogress = function(e) {
                if (e.lengthComputable) {
                    let percent = Math.round((e.loaded / e.total) * 100);
                    progressBar.style.width = percent + '%';
                    progressBar.textContent = percent + '%';
                }
            };
            xhr.onload = function() {
                if (xhr.status >= 200 && xhr.status < 300) {
                    progressBar.style.width = '100%';
                    progressBar.textContent = '100%';
                    window.location.reload();
                } else {
                    uploadError.textContent = 'Erro ao enviar arquivo: ' + xhr.statusText;
                    uploadError.classList.remove('d-none');
                    progressBarContainer.style.display = 'none';
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="fas fa-save me-1"></i> Salvar Capítulo';
                }
            };
            xhr.onerror = function() {
                uploadError.textContent = 'Erro de conexão durante o upload.';
                uploadError.classList.remove('d-none');
                progressBarContainer.style.display = 'none';
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-save me-1"></i> Salvar Capítulo';
            };
            xhr.send(formData);
        });
    });
</script>
{% endblock %}

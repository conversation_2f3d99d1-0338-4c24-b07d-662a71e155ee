{% extends 'base.html' %}
{% load static %}
{% load manga_permissions %}

{% block title %}{{ manga.title }} - Mangás - Project Nix{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm border-0 mb-4">
                <div class="row g-0">
                    {% if manga.cover_image %}
                    <div class="col-md-4">
                        <img src="{{ manga.cover_image.url }}" class="img-fluid rounded-start w-100 h-100 object-fit-cover" alt="{{ manga.title }}">
                    </div>
                    {% endif %}
                    <div class="col-md-8">
                        <div class="card-body">
                            <h1 class="card-title h3 mb-2">{{ manga.title }}</h1>
                            {% if manga.author %}
                            <p class="mb-2"><i class="fas fa-user me-1"></i> {{ manga.author }}</p>
                            {% endif %}
                            <p class="card-text">{{ manga.description }}</p>
                            <div class="mt-4 d-flex gap-2">
                                {% if user|can_edit_manga:manga %}
                                <a href="{% url 'mangas:manga_edit' manga.slug %}" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-edit"></i> Editar
                                </a>
                                <a href="{% url 'mangas:manga_delete' manga.slug %}" class="btn btn-outline-danger btn-sm">
                                    <i class="fas fa-trash"></i> Deletar
                                </a>
                                {% endif %}
                                <a href="{% url 'mangas:manga_list' %}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-arrow-left"></i> Voltar
                                </a>
                                {% if user|has_manga_permission %}
                                <a href="{% url 'mangas:capitulo_create' manga.slug %}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus"></i> Novo Capítulo
                                </a>
                                <a href="{% url 'mangas:capitulo_complete_create' manga.slug %}" class="btn btn-success btn-sm">
                                    <i class="fas fa-file-archive"></i> Upload Completo
                                </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-theme-secondary text-theme-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 text-sans text-body">
                        <i class="fas fa-book me-2"></i>Volumes
                    </h5>
                    <div class="btn-group">
                        {% if user|has_manga_permission %}
                        <a href="{% url 'mangas:volume_create' manga.slug %}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus me-1"></i> Adicionar Volume
                        </a>
                        <a href="{% url 'mangas:capitulo_create' manga.slug %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-plus-circle me-1"></i> Adicionar Capítulo
                        </a>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    {% if volumes %}
                        <div class="accordion" id="volumesAccordion">
                            {% for volume in volumes %}
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="heading{{ volume.number }}">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ volume.number }}" aria-expanded="false" aria-controls="collapse{{ volume.number }}">
                                        <div class="d-flex align-items-center w-100">
                                            <div class="me-auto">
                                                <span class="fw-bold">Volume {{ volume.number }}</span>
                                                {% if volume.title %}
                                                <span class="ms-2">{{ volume.title }}</span>
                                                {% endif %}
                                            </div>
                                            <div class="ms-2">
                                                <span class="badge bg-primary rounded-pill">
                                                    {{ volume.capitulos.count }} capítulo{{ volume.capitulos.count|pluralize }}
                                                </span>
                                                {% if volume.cover_image %}
                                                <span class="badge bg-info rounded-pill ms-1" title="Possui capa">
                                                    <i class="fas fa-image"></i>
                                                </span>
                                                {% endif %}
                                                {% if not volume.is_published %}
                                                <span class="badge bg-warning text-dark rounded-pill ms-1" title="Rascunho">
                                                    <i class="fas fa-eye-slash"></i>
                                                </span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </button>
                                </h2>
                                <div id="collapse{{ volume.number }}" class="accordion-collapse collapse" aria-labelledby="heading{{ volume.number }}" data-bs-parent="#volumesAccordion">
                                    <div class="accordion-body p-0">
                                        {% if user|has_manga_permission %}
                                        <div class="d-flex justify-content-end p-2 bg-light border-bottom">
                                            <a href="{% url 'mangas:volume_edit' manga.slug volume.slug %}" class="btn btn-sm btn-outline-secondary me-1">
                                                <i class="fas fa-edit me-1"></i> Editar Volume
                                            </a>
                                            <a href="{% url 'mangas:volume_delete' manga.slug volume.slug %}" class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash me-1"></i> Excluir
                                            </a>
                                        </div>
                                        {% endif %}
                                        
                                        <div class="list-group list-group-flush">
                                            {% if volume.capitulos.exists %}
                                                {% for capitulo in volume.capitulos.all %}
                                                <div class="list-group-item">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <a href="{% url 'mangas:capitulo_detail' manga_slug=manga.slug capitulo_slug=capitulo.slug %}" class="text-decoration-none d-flex align-items-center">
                                                                <i class="fas fa-file-alt me-2"></i>
                                                                <div>
                                                                                                                    <div class="fw-medium">
                                                    Capítulo {{ capitulo.number }}{% if capitulo.title %}: {{ capitulo.title }}{% endif %}
                                                    {% if not capitulo.is_published and user|has_manga_permission %}
                                                    <span class="badge bg-warning text-dark ms-1" title="Rascunho">
                                                        <i class="fas fa-eye-slash"></i>
                                                    </span>
                                                    {% endif %}
                                                </div>
                                                <small class="text-muted">{{ capitulo.num_paginas|default:0 }} página{{ capitulo.num_paginas|default:0|pluralize }}</small>
                                                                </div>
                                                            </a>
                                                        </div>
                                                        <div class="btn-group">
                                                            <a href="{% url 'mangas:capitulo_detail' manga_slug=manga.slug capitulo_slug=capitulo.slug %}" class="btn btn-sm btn-outline-primary" title="Ler capítulo">
                                                                <i class="fas fa-book-reader"></i> Ler
                                                            </a>
                                                            {% if user|has_manga_permission %}
                                                            <a href="{% url 'mangas:capitulo_edit' manga_slug=manga.slug capitulo_slug=capitulo.slug %}" class="btn btn-sm btn-outline-secondary" title="Editar capítulo">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <a href="{% url 'mangas:capitulo_delete' manga_slug=manga.slug capitulo_slug=capitulo.slug %}" class="btn btn-sm btn-outline-danger" title="Excluir capítulo">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                            {% endif %}
                                                        </div>
                                                    </div>
                                                </div>
                                                {% endfor %}
                                            {% else %}
                                                <div class="text-center p-4 text-muted">
                                                    <i class="fas fa-inbox fa-2x mb-2"></i>
                                                    <p class="mb-0">Nenhum capítulo cadastrado neste volume.</p>
                                                    {% if user|has_manga_permission %}
                                                    <div class="mt-2">
                                                        <a href="{% url 'mangas:capitulo_create' manga.slug %}?volume={{ volume.id }}" class="btn btn-sm btn-outline-primary me-1">
                                                            <i class="fas fa-plus me-1"></i> Adicionar Capítulo
                                                        </a>
                                                        <a href="{% url 'mangas:capitulo_complete_create' manga.slug %}?volume={{ volume.id }}" class="btn btn-sm btn-outline-success">
                                                            <i class="fas fa-file-archive me-1"></i> Upload Completo
                                                        </a>
                                                    </div>
                                                    {% endif %}
                                                </div>
                                            {% endif %}
                                        </div>
                                        
                                        {% if user|has_manga_permission and volume.capitulos.exists %}
                                        <div class="text-center p-2 bg-light border-top">
                                            <a href="{% url 'mangas:capitulo_create' manga.slug %}?volume={{ volume.id }}" class="btn btn-sm btn-outline-primary me-1">
                                                <i class="fas fa-plus me-1"></i> Adicionar Capítulo
                                            </a>
                                            <a href="{% url 'mangas:capitulo_complete_create' manga.slug %}?volume={{ volume.id }}" class="btn btn-sm btn-outline-success">
                                                <i class="fas fa-file-archive me-1"></i> Upload Completo
                                            </a>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        
                        <!-- Paginação -->
                        {% if volumes.has_other_pages %}
                        <nav aria-label="Navegação de volumes" class="mt-4">
                            <ul class="pagination justify-content-center">
                                {% if volumes.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ volumes.previous_page_number }}" aria-label="Anterior">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">&laquo;</span>
                                    </li>
                                {% endif %}
                                
                                {% for i in volumes.paginator.page_range %}
                                    {% if volumes.number == i %}
                                        <li class="page-item active"><span class="page-link">{{ i }}</span></li>
                                    {% else %}
                                        <li class="page-item"><a class="page-link" href="?page={{ i }}">{{ i }}</a></li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if volumes.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ volumes.next_page_number }}" aria-label="Próximo">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">&raquo;</span>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}
                        
                    {% else %}
                        <div class="alert alert-info">
                            <p class="mb-3">Nenhum volume cadastrado para este mangá.</p>
                            <a href="{% url 'mangas:volume_create' manga.slug %}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i> Adicionar Primeiro Volume
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Garante que todos os acordeões iniciem fechados
    const accordionButtons = document.querySelectorAll('.accordion-button');
    const accordionCollapses = document.querySelectorAll('.accordion-collapse');
    
    accordionButtons.forEach(button => {
        button.classList.add('collapsed');
        button.setAttribute('aria-expanded', 'false');
    });
    
    accordionCollapses.forEach(collapse => {
        collapse.classList.remove('show');
    });
});
</script>
{% endblock %} 
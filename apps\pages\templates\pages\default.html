{% extends "base.html" %}
{% load static %}

{% block title %}{{ page.title }} - {{ block.super }}{% endblock %}

{% block meta_description %}{{ page.excerpt|default:page.title }}{% endblock %}
{% block meta_keywords %}{{ page.meta_keywords|default:"" }}{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <!-- Cabe<PERSON><PERSON><PERSON> da página -->
            <header class="page-header mb-4">
                <h1 class="page-title">{{ page.title }}</h1>
                {% if page.excerpt %}
                    <p class="page-excerpt text-muted">{{ page.excerpt }}</p>
                {% endif %}
                
                <!-- Meta informações -->
                {% if page.created_at %}
                    <div class="page-meta text-muted small">
                        <i class="fas fa-calendar-alt me-1"></i>
                        Criad<PERSON> em {{ page.created_at|date:"d/m/Y" }}
                        {% if page.updated_at and page.updated_at != page.created_at %}
                            • Atualizado em {{ page.updated_at|date:"d/m/Y" }}
                        {% endif %}
                        {% if page.view_count %}
                            • {{ page.view_count }} visualizações
                        {% endif %}
                    </div>
                {% endif %}
            </header>

            <!-- Conteúdo da página -->
            <main class="page-content">
                {% if page.content %}
                    <div class="content-body">
                        {{ page.content|safe }}
                    </div>
                {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Esta página ainda não possui conteúdo.
                    </div>
                {% endif %}
            </main>

            <!-- Rodapé da página -->
            {% if page.tags.exists or page.created_by %}
                <footer class="page-footer mt-5 pt-4 border-top">
                    <div class="row">
                        {% if page.tags.exists %}
                            <div class="col-md-8">
                                <h6 class="text-muted mb-2">Tags:</h6>
                                <div class="tags">
                                    {% for tag in page.tags.all %}
                                        <span class="badge bg-secondary me-1">{{ tag.name }}</span>
                                    {% endfor %}
                                </div>
                            </div>
                        {% endif %}
                        
                        {% if page.created_by %}
                            <div class="col-md-4 text-md-end">
                                <h6 class="text-muted mb-2">Autor:</h6>
                                <div class="author-info">
                                    {% if page.created_by.get_full_name %}
                                        {{ page.created_by.get_full_name }}
                                    {% else %}
                                        {{ page.created_by.username }}
                                    {% endif %}
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </footer>
            {% endif %}
        </div>
    </div>
</div>

<!-- Navegação entre páginas -->
{% if related_pages %}
    <section class="related-pages bg-light py-5">
        <div class="container">
            <h3 class="text-center mb-4">Páginas Relacionadas</h3>
            <div class="row">
                {% for related_page in related_pages %}
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <a href="{% url 'pages:page_detail' related_page.slug %}" class="text-decoration-none">
                                        {{ related_page.title }}
                                    </a>
                                </h5>
                                {% if related_page.excerpt %}
                                    <p class="card-text text-muted">{{ related_page.excerpt|truncatewords:20 }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
    </section>
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
.page-header {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 1rem;
}

.page-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.page-excerpt {
    font-size: 1.1rem;
    line-height: 1.6;
}

.page-meta {
    margin-top: 0.5rem;
}

.content-body {
    font-size: 1.05rem;
    line-height: 1.7;
    color: #333;
}

.content-body h1,
.content-body h2,
.content-body h3,
.content-body h4,
.content-body h5,
.content-body h6 {
    color: #2c3e50;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.content-body p {
    margin-bottom: 1.2rem;
}

.content-body img {
    max-width: 100%;
    height: auto;
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.content-body blockquote {
    border-left: 4px solid #007bff;
    padding-left: 1rem;
    margin: 1.5rem 0;
    font-style: italic;
    color: #6c757d;
}

.page-footer {
    background-color: #f8f9fa;
    margin: 0 -15px;
    padding: 1.5rem 15px;
    border-radius: 0.375rem;
}

.tags .badge {
    font-size: 0.875rem;
}

.author-info {
    font-weight: 500;
    color: #495057;
}

.related-pages .card {
    transition: transform 0.2s ease-in-out;
}

.related-pages .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Adicionar classes Bootstrap a elementos do conteúdo
    const contentBody = document.querySelector('.content-body');
    if (contentBody) {
        // Adicionar classes a tabelas
        const tables = contentBody.querySelectorAll('table');
        tables.forEach(table => {
            table.classList.add('table', 'table-striped', 'table-responsive');
        });
        
        // Adicionar classes a imagens
        const images = contentBody.querySelectorAll('img');
        images.forEach(img => {
            img.classList.add('img-fluid');
        });
    }
    
    // Smooth scroll para links internos
    const internalLinks = document.querySelectorAll('a[href^="#"]');
    internalLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});
</script>
{% endblock %}

{% extends 'base.html' %}
{% load static %}
{% load articles_tags %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'articles/css/article-list.css' %}">

{% endblock %}

{% block extra_js %}
<script src="{% static 'articles/js/article-list.js' %}"></script>
{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row">
        <div class="col-lg-8">
            {# Seção de artigos em destaque #}
            {% if featured_articles %}
            <div class="mb-5">
                <h2 class="h4 mb-4 text-theme-primary text-sans"><i class="fas fa-star me-2"></i>Conteúdo em Destaque</h2>
                <div class="row g-4">
                    {% for article in featured_articles %}
                    <div class="col-md-6 col-12">
                        <article class="card h-100 shadow-sm article-card border-primary border-2">
                            {% if article.featured_image %}
                            <div class="position-relative">
                                <img src="{{ article.featured_image.url }}"
                                     class="card-img-top"
                                     alt="{{ article.title }}"
                                     style="height: 200px; object-fit: cover;">
                                {% if article.category %}
                                <span class="badge bg-primary position-absolute top-0 start-0 m-2">
                                    {{ article.category.name }}
                                </span>
                                {% endif %}
                            </div>
                            {% endif %}
                            <div class="card-body d-flex flex-column">
                                <h5 class="card-title mb-2">
                                    <a href="{{ article.get_absolute_url }}" class="text-decoration-none text-dark">
                                        {{ article.title }}
                                    </a>
                                </h5>
                                <p class="card-text flex-grow-1">
                                    {{ article.excerpt|clean_excerpt:100 }}
                                </p>
                                <div class="mt-auto d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-user me-1"></i>
                                        {{ article.author.get_full_name|default:article.author.username }}
                                    </small>
                                    <a href="{{ article.get_absolute_url }}" class="btn btn-outline-primary btn-sm ms-2">
                                        <i class="fas fa-book-open me-1"></i>Ler
                                    </a>
                                </div>
                            </div>
                        </article>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h2 mb-1 text-sans text-body">
                        <i class="fas fa-newspaper me-2 text-theme-primary"></i>
                        {% if category %}
                            Artigos em <span class="text-theme-primary">{{ category.name }}</span>
                        {% else %}
                            Todos os Artigos
                        {% endif %}
                    </h1>
                    <p class="text-theme-secondary mb-0 text-body">Leitura de artigos, livros, mangás e audiolivros</p>
                </div>
                <div>
                    {% if user.is_authenticated and user.is_staff or user.is_authenticated and user.is_superuser or user.is_authenticated and user|has_group:'Editor' %}
                        <a href="{% url 'articles:article_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Novo Artigo
                        </a>
                    {% endif %}
                </div>
            </div>
            {% if articles %}
                <div class="row g-4">
                    {% for article in articles %}
                    <div class="col-lg-6 col-md-6 col-12">
                        <article class="card h-100 shadow-sm article-card">
                            {% if article.featured_image %}
                            <div class="position-relative">
                                <img src="{{ article.featured_image.url }}"
                                     class="card-img-top"
                                     alt="{{ article.title }}"
                                     style="height: 250px; object-fit: cover;">
                                {% if article.category %}
                                <span class="badge bg-primary position-absolute top-0 start-0 m-2">
                                    {{ article.category.name }}
                                </span>
                                {% endif %}
                            </div>
                            {% endif %}

                            <div class="card-body d-flex flex-column">
                                <h5 class="card-title mb-2">
                                    <a href="{{ article.get_absolute_url }}" class="text-decoration-none text-dark">
                                        {{ article.title }}
                                    </a>
                                </h5>

                                {% if article.subtitle %}
                                <h6 class="card-subtitle mb-3 text-muted">{{ article.subtitle }}</h6>
                                {% endif %}

                                <p class="card-text flex-grow-1">
                                    {{ article.excerpt|clean_excerpt:120 }}
                                </p>

                                <div class="mt-auto">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <small class="text-muted">
                                            <i class="fas fa-user me-1"></i>
                                            {{ article.author.get_full_name|default:article.author.username }}
                                        </small>
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            {{ article.published_at|date:'d/m/Y' }}
                                        </small>
                                    </div>

                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="article-stats">
                                            <small class="text-muted me-3">
                                                <i class="fas fa-eye me-1"></i>
                                                {{ article.view_count|default:0 }}
                                            </small>
                                            <small class="text-muted">
                                                <i class="fas fa-comments me-1"></i>
                                                {{ article.comment_count|default:0 }}
                                            </small>
                                        </div>

                                        <div class="article-actions">
                                            <a href="{{ article.get_absolute_url }}"
                                               class="btn btn-primary btn-sm">
                                                <i class="fas fa-book-open me-1"></i>Ler
                                            </a>
                                            {% if user.is_staff and article.get_edit_url %}
                                            <a href="{{ article.get_edit_url }}"
                                               class="btn btn-outline-secondary btn-sm ms-1">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </article>
                    </div>
                    {% endfor %}
                </div>
                {% if page_obj.has_other_pages %}
                <nav aria-label="Paginação" class="mt-5">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Anterior</a>
                            </li>
                        {% endif %}
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}">Próximo</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-newspaper fa-5x text-theme-secondary mb-3"></i>
                    <h3 class="text-theme-secondary text-sans text-body">Nenhum artigo encontrado</h3>
                    <p class="text-theme-secondary text-body">Ainda não há artigos publicados.</p>
                </div>
            {% endif %}
        </div>
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm mb-4 article-list-card">
                <div class="card-header bg-theme-secondary text-theme-light">
                    <h6 class="mb-0"><i class="fas fa-search me-2"></i>Buscar Artigos</h6>
                </div>
                <div class="card-body">
                    <form method="get" action="{% url 'articles:search' %}" aria-label="Formulário de busca" role="form">
                        <div class="input-group">
                            <input type="text" name="q" class="form-control" placeholder="Digite sua busca...">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            <div class="card border-0 shadow-sm mb-4 article-list-card">
                <div class="card-header bg-theme-info text-theme-light">
                    <h6 class="mb-0"><i class="fas fa-folder me-2"></i>Categorias</h6>
                </div>
                <div class="card-body">
                    <ul class="list-group mb-4">
                        {% if categories %}
                            {% for cat in categories %}
                                <a href="{{ cat.get_absolute_url }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center {% if category and cat.pk == category.pk %}active{% endif %}">
                                    {% if cat.icon %}<i class="{{ cat.icon }} me-2"></i>{% endif %}
                                    {{ cat.name }}
                                    <span class="badge bg-theme-primary rounded-pill">{{ cat.articles.count }}</span>
                                </a>
                            {% endfor %}
                        {% else %}
                            <li class="list-group-item text-center text-muted">Nenhuma categoria cadastrada.</li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

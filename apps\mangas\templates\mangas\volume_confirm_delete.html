{% extends 'base.html' %}

{% block title %}Deletar Volume - {{ object.manga.title }} - Project Nix{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'mangas:manga_list' %}">Mangás</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'mangas:manga_detail' object.manga.slug %}">{{ object.manga.title|truncatechars:20 }}</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Deletar Volume</li>
                </ol>
            </nav>
            
            <div class="card shadow-sm border-0">
                <div class="card-body">
                    <h1 class="h4 mb-4">
                        Confirma<PERSON>
                        <small class="text-muted d-block mt-1">{{ object.manga.title }} - Volume {{ object.number }}{% if object.title %}: {{ object.title }}{% endif %}</small>
                    </h1>
                    
                    <div class="alert alert-warning">
                        <h5 class="alert-heading">Atenção!</h5>
                        <p class="mb-0">Você está prestes a excluir este volume e todos os seus capítulos e páginas associados. Esta ação não pode ser desfeita.</p>
                    </div>
                    
                    <div class="card bg-light mb-4">
                        <div class="card-body">
                            <h6 class="card-subtitle mb-2 text-muted">Detalhes do Volume</h6>
                            <p class="card-text mb-1"><strong>Título:</strong> Volume {{ object.number }}{% if object.title %}: {{ object.title }}{% endif %}</p>
                            <p class="card-text mb-1"><strong>Mangá:</strong> {{ object.manga.title }}</p>
                            <p class="card-text mb-1"><strong>Capítulos:</strong> {{ object.capitulos.count }} capítulo{{ object.capitulos.count|pluralize }} neste volume</p>
                            <p class="card-text mb-0"><strong>Data de Criação:</strong> {{ object.created_at|date:"d/m/Y H:i" }}</p>
                        </div>
                    </div>
                    
                    <form method="post">
                        {% csrf_token %}
                        <div class="d-flex justify-content-end gap-2 mt-4">
                            <a href="{% if request.META.HTTP_REFERER %}{{ request.META.HTTP_REFERER }}{% else %}{% url 'mangas:manga_detail' object.manga.slug %}{% endif %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i> Cancelar
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash-alt me-1"></i> Confirmar Exclusão
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% extends 'config/base_config_page.html' %}
{% load config_extras %}

{% block config_title %}Moderação de Comentários{% endblock %}
{% block page_icon %}<i class="fas fa-shield-alt me-2"></i>{% endblock %}
{% block page_title %}Moderação de Comentários{% endblock %}
{% block page_actions %}
<a href="{% url 'articles:comment_stats' %}" class="btn btn-outline-info">
    <i class="fas fa-chart-bar me-2"></i>Estatísticas
</a>
{% endblock %}

{% block page_content %}
<!-- Estatísticas -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card shadow-sm border-0">
            <div class="card-body text-center">
                <div class="h3 text-primary mb-1">{{ stats.total }}</div>
                <small class="text-muted">Total de Comentários</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card shadow-sm border-0">
            <div class="card-body text-center">
                <div class="h3 text-warning mb-1">{{ stats.pending }}</div>
                <small class="text-muted">Aguardando Aprovação</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card shadow-sm border-0">
            <div class="card-body text-center">
                <div class="h3 text-success mb-1">{{ stats.approved }}</div>
                <small class="text-muted">Aprovados</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card shadow-sm border-0">
            <div class="card-body text-center">
                <div class="h3 text-danger mb-1">{{ stats.spam }}</div>
                <small class="text-muted">Spam</small>
            </div>
        </div>
    </div>
</div>

<!-- Filtros e Busca -->
<div class="card shadow-sm mb-4">
    <div class="card-body">
        <!-- Filtros -->
        <div class="mb-3">
            <ul class="nav nav-pills">
                <li class="nav-item">
                    <a class="nav-link {% if status_filter == 'pending' %}active{% endif %}" 
                       href="?status=pending">
                        <i class="fas fa-clock me-1"></i>
                        Pendentes ({{ stats.pending }})
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if status_filter == 'approved' %}active{% endif %}" 
                       href="?status=approved">
                        <i class="fas fa-check me-1"></i>
                        Aprovados ({{ stats.approved }})
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if status_filter == 'spam' %}active{% endif %}" 
                       href="?status=spam">
                        <i class="fas fa-ban me-1"></i>
                        Spam ({{ stats.spam }})
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if not status_filter %}active{% endif %}" 
                       href="?">
                        <i class="fas fa-list me-1"></i>
                        Todos
                    </a>
                </li>
            </ul>
        </div>

        <!-- Busca -->
        <form method="get" class="row g-3">
            <div class="col-md-8">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" name="search" 
                           value="{{ search }}" placeholder="Buscar por conteúdo, autor, email ou artigo...">
                    <input type="hidden" name="status" value="{{ status_filter }}">
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>
                        Buscar
                    </button>
                    {% if search %}
                    <a href="?status={{ status_filter }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        Limpar
                    </a>
                    {% endif %}
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Lista de Comentários -->
{% if comments %}
    <!-- Desktop View -->
    <div class="d-none d-md-block">
        {% for comment in comments %}
        <div class="card shadow-sm mb-3" id="comment-{{ comment.id }}">
            <div class="card-header bg-light">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h6 class="mb-1">
                            <i class="fas fa-user me-1"></i>
                            {{ comment.author_name }}
                            {% if comment.user %}
                                <span class="badge bg-info ms-1">Registrado</span>
                            {% endif %}
                            {% if comment.is_reply %}
                                <span class="badge bg-secondary ms-1">Resposta</span>
                            {% endif %}
                        </h6>
                        <small class="text-muted">
                            <i class="fas fa-envelope me-1"></i> {{ comment.email }}
                            {% if comment.website %}
                                | <i class="fas fa-globe me-1"></i> 
                                <a href="{{ comment.website }}" target="_blank" rel="nofollow">
                                    {{ comment.website|truncatechars:30 }}
                                </a>
                            {% endif %}
                        </small>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="mb-1">
                            {% if comment.is_approved %}
                                <span class="badge bg-success">Aprovado</span>
                            {% elif comment.is_spam %}
                                <span class="badge bg-danger">Spam</span>
                            {% else %}
                                <span class="badge bg-warning">Pendente</span>
                            {% endif %}
                        </div>
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i> {{ comment.created_at|date:"d/m/Y H:i" }}
                            {% if comment.approved_at %}
                                <br><i class="fas fa-check me-1"></i> {{ comment.approved_at|date:"d/m/Y H:i" }}
                            {% endif %}
                        </small>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Artigo -->
                <div class="mb-3">
                    <strong>Artigo:</strong>
                    <a href="{% url 'articles:article_detail' comment.article.slug %}" target="_blank" class="text-decoration-none">
                        {{ comment.article.title }}
                        <i class="fas fa-external-link-alt fa-xs"></i>
                    </a>
                </div>

                <!-- Comentário pai (se for resposta) -->
                {% if comment.parent %}
                <div class="alert alert-info mb-3">
                    <strong>Em resposta a {{ comment.parent.author_name }}:</strong>
                    <div class="mt-1">{{ comment.parent.content|truncatechars:100 }}</div>
                </div>
                {% endif %}

                <!-- Conteúdo -->
                <div class="mb-3">
                    <div class="bg-light p-3 rounded">
                        {{ comment.content|linebreaks }}
                    </div>
                </div>

                <!-- Dados técnicos -->
                <small class="text-muted">
                    {% if comment.ip_address %}
                        <i class="fas fa-map-marker-alt me-1"></i> IP: {{ comment.ip_address }}
                    {% endif %}
                    {% if comment.user_agent %}
                        | <i class="fas fa-desktop me-1"></i> {{ comment.user_agent|truncatechars:50 }}
                    {% endif %}
                </small>
            </div>
            <div class="card-footer bg-light">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <a href="{% url 'articles:article_detail' comment.article.slug %}#comment-{{ comment.id }}"
                           target="_blank" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye me-1"></i>
                            Ver no Site
                        </a>
                    </div>
                    <div class="btn-group btn-group-sm">
                        {% if not comment.is_approved and not comment.is_spam %}
                            <form method="post" action="{% url 'articles:moderate_comment_action' comment.id %}" class="d-inline">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="approve">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-check me-1"></i>
                                    Aprovar
                                </button>
                            </form>
                        {% endif %}

                        {% if not comment.is_spam %}
                            <form method="post" action="{% url 'articles:moderate_comment_action' comment.id %}" class="d-inline">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="spam">
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-ban me-1"></i>
                                    Spam
                                </button>
                            </form>
                        {% endif %}

                        <form method="post" action="{% url 'articles:moderate_comment_action' comment.id %}" class="d-inline">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="delete">
                            <button type="submit" class="btn btn-danger" 
                                    onclick="return confirm('Tem certeza que deseja excluir este comentário?')">
                                <i class="fas fa-trash me-1"></i>
                                Excluir
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Mobile View -->
    <div class="d-block d-md-none">
        {% for comment in comments %}
        <div class="card shadow-sm mb-3" id="comment-{{ comment.id }}">
            <div class="card-header bg-light">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <div>
                        <h6 class="mb-1">
                            <i class="fas fa-user me-1"></i>
                            {{ comment.author_name }}
                        </h6>
                        <small class="text-muted">{{ comment.email }}</small>
                    </div>
                    <div class="text-end">
                        {% if comment.is_approved %}
                            <span class="badge bg-success">Aprovado</span>
                        {% elif comment.is_spam %}
                            <span class="badge bg-danger">Spam</span>
                        {% else %}
                            <span class="badge bg-warning">Pendente</span>
                        {% endif %}
                    </div>
                </div>
                <small class="text-muted">
                    <i class="fas fa-clock me-1"></i> {{ comment.created_at|date:"d/m/Y H:i" }}
                </small>
            </div>
            <div class="card-body">
                <!-- Artigo -->
                <div class="mb-2">
                    <small class="text-muted">Artigo:</small>
                    <div>
                        <a href="{% url 'articles:article_detail' comment.article.slug %}" target="_blank" class="text-decoration-none">
                            {{ comment.article.title|truncatechars:50 }}
                        </a>
                    </div>
                </div>

                <!-- Conteúdo -->
                <div class="mb-3">
                    <div class="bg-light p-2 rounded">
                        {{ comment.content|linebreaks|truncatechars:200 }}
                    </div>
                </div>

                <!-- Ações -->
                <div class="d-grid gap-2">
                    {% if not comment.is_approved and not comment.is_spam %}
                        <form method="post" action="{% url 'articles:moderate_comment_action' comment.id %}">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="approve">
                            <button type="submit" class="btn btn-success btn-sm w-100">
                                <i class="fas fa-check me-1"></i>
                                Aprovar
                            </button>
                        </form>
                    {% endif %}

                    {% if not comment.is_spam %}
                        <form method="post" action="{% url 'articles:moderate_comment_action' comment.id %}">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="spam">
                            <button type="submit" class="btn btn-warning btn-sm w-100">
                                <i class="fas fa-ban me-1"></i>
                                Marcar como Spam
                            </button>
                        </form>
                    {% endif %}

                    <form method="post" action="{% url 'articles:moderate_comment_action' comment.id %}">
                        {% csrf_token %}
                        <input type="hidden" name="action" value="delete">
                        <button type="submit" class="btn btn-danger btn-sm w-100" 
                                onclick="return confirm('Tem certeza que deseja excluir este comentário?')">
                            <i class="fas fa-trash me-1"></i>
                            Excluir
                        </button>
                    </form>

                    <a href="{% url 'articles:article_detail' comment.article.slug %}#comment-{{ comment.id }}"
                       target="_blank" class="btn btn-outline-primary btn-sm w-100">
                        <i class="fas fa-eye me-1"></i>
                        Ver no Site
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Paginação -->
    {% if comments.has_other_pages %}
    <nav aria-label="Paginação" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if comments.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ comments.previous_page_number }}&status={{ status_filter }}&search={{ search }}">
                        <i class="fas fa-chevron-left"></i>
                        Anterior
                    </a>
                </li>
            {% endif %}

            {% for num in comments.paginator.page_range %}
                {% if comments.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                {% elif num > comments.number|add:'-3' and num < comments.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}&status={{ status_filter }}&search={{ search }}">{{ num }}</a>
                    </li>
                {% endif %}
            {% endfor %}

            {% if comments.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ comments.next_page_number }}&status={{ status_filter }}&search={{ search }}">
                        Próxima
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}

{% else %}
    <div class="card shadow-sm">
        <div class="card-body text-center py-5">
            <i class="fas fa-comments fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Nenhum comentário encontrado</h5>
            {% if search %}
                <p class="text-muted">Tente ajustar os termos de busca.</p>
            {% else %}
                <p class="text-muted">Não há comentários para moderar no momento.</p>
            {% endif %}
        </div>
    </div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
// Auto-refresh para comentários pendentes
var statusFilter = '{{ status_filter }}';
if (statusFilter === 'pending') {
    setTimeout(function() {
        location.reload();
    }, 60000); // Refresh a cada 1 minuto
}

// Confirmação para ações
document.addEventListener('DOMContentLoaded', function() {
    var forms = document.querySelectorAll('form[action*="moderar"]');
    forms.forEach(function(form) {
        form.addEventListener('submit', function(e) {
            var action = this.querySelector('input[name="action"]').value;
            var message = '';
            
            switch(action) {
                case 'approve':
                    message = 'Aprovar este comentário?';
                    break;
                case 'spam':
                    message = 'Marcar este comentário como spam?';
                    break;
                case 'delete':
                    message = 'Excluir permanentemente este comentário?';
                    break;
            }
            
            if (message && !confirm(message)) {
                e.preventDefault();
            }
        });
    });
});
</script>
{% endblock %}

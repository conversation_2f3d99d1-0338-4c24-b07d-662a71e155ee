# Generated by Django 5.2.2 on 2025-06-06 20:33

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('config', '0002_databaseconfiguration_emailconfiguration'),
    ]

    operations = [
        migrations.AlterField(
            model_name='emailconfiguration',
            name='email_host_password',
            field=models.CharField(blank=True, help_text='Senha ou senha de app', max_length=200, verbose_name='Senha SMTP'),
        ),
        migrations.AlterField(
            model_name='emailconfiguration',
            name='email_host_user',
            field=models.CharField(blank=True, help_text='<PERSON>ail ou username para autenticação', max_length=200, verbose_name='<PERSON><PERSON><PERSON><PERSON> SMTP'),
        ),
    ]

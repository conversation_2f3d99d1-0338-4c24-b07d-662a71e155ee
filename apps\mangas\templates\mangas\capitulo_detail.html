{% extends 'base.html' %}
{% load static %}
{% load manga_permissions %}

{% block title %}{{ capitulo.manga.title }} - {% if capitulo.volume %}Volume {{ capitulo.volume.number }} - {% endif %}Capítulo {{ capitulo.number }}{% if capitulo.title %}: {{ capitulo.title }}{% endif %} - Mangás - Project Nix{% endblock %}

{% block extra_css %}
<style>
    /* Estilos para o modo tela cheia */
    .fullscreen-controls {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        padding: 10px 20px;
        background-color: rgba(0, 0, 0, 0.7);
        color: white;
        z-index: 9999;
        display: none;
        justify-content: space-between;
        align-items: center;
    }
    
    .fullscreen-controls .chapter-info {
        font-weight: bold;
    }
    
    .fullscreen-controls .page-info {
        margin: 0 20px;
    }
    
    .fullscreen-controls .nav-buttons button {
        background: none;
        border: 1px solid white;
        color: white;
        padding: 5px 15px;
        margin: 0 5px;
        border-radius: 4px;
        cursor: pointer;
    }
    
    .fullscreen-controls .nav-buttons button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
    
    .fullscreen-controls .close-btn {
        background: none;
        border: none;
        color: white;
        font-size: 1.5rem;
        cursor: pointer;
    }
    
    .fullscreen-container {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: #000;
        z-index: 9998;
        overflow-y: auto;
    }
    
    .fullscreen-page {
        display: none;
        max-width: 100%;
        max-height: 100vh;
        margin: 0 auto;
        padding: 60px 20px 20px;
        min-height: calc(100vh - 80px);
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: relative;
    }
    
    .fullscreen-page.active {
        display: flex;
    }
    
    .fullscreen-page img {
        max-width: 100%;
        max-height: calc(100vh - 120px);
        margin: 0 auto;
        display: block;
        object-fit: contain;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }
    
    .fullscreen-page .page-number {
        color: white;
        text-align: center;
        margin-top: 10px;
        font-size: 0.9rem;
        opacity: 0.8;
        background-color: rgba(0,0,0,0.5);
        padding: 4px 8px;
        border-radius: 4px;
    }
    
    #fs-lazy-pages-container {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    
    .fullscreen-toggle {
        cursor: pointer;
        white-space: nowrap;
    }
</style>
{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="mb-4">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'mangas:manga_list' %}">Mangás</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'mangas:manga_detail' capitulo.manga.slug %}">{{ capitulo.manga.title }}</a></li>
                        {% if capitulo.volume %}
                        <li class="breadcrumb-item"><a href="{% url 'mangas:manga_detail' capitulo.manga.slug %}#volume-{{ capitulo.volume.number }}">Volume {{ capitulo.volume.number }}{% if capitulo.volume.title %}: {{ capitulo.volume.title }}{% endif %}</a></li>
                        {% endif %}
                        <li class="breadcrumb-item active" aria-current="page">Capítulo {{ capitulo.number }}{% if capitulo.title %}: {{ capitulo.title }}{% endif %}</li>
                    </ol>
                </nav>
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <div>
                        <h1 class="h4 mb-1">{{ capitulo.manga.title }}</h1>
                        <div class="d-flex align-items-center flex-wrap gap-2">
                            {% if capitulo.volume %}
                            <a href="{% url 'mangas:volume_detail' manga_slug=capitulo.manga.slug volume_slug=capitulo.volume.slug %}" class="text-decoration-none">
                                <span class="badge bg-primary">
                                    <i class="fas fa-book me-1"></i> Volume {{ capitulo.volume.number }}{% if capitulo.volume.title %}: {{ capitulo.volume.title }}{% endif %}
                                </span>
                            </a>
                            {% endif %}
                            <span class="badge bg-secondary">
                                <i class="fas fa-file-alt me-1"></i> Capítulo {{ capitulo.number }}{% if capitulo.title %}: {{ capitulo.title }}{% endif %}
                            </span>
                            {% if capitulo.paginas.all %}
                            <span class="badge bg-info text-dark">
                                <i class="fas fa-file-image me-1"></i> {{ capitulo.paginas.count }} página{{ capitulo.paginas.count|pluralize }}
                            </span>
                            {% endif %}
                            {% if capitulo.publication_date %}
                            <span class="badge bg-light text-dark border">
                                <i class="far fa-calendar-alt me-1"></i> {{ capitulo.publication_date|date:"d/m/Y" }}
                            </span>
                            {% endif %}
                        </div>
                    </div>
                    {% if capitulo.paginas.all %}
                    <button id="fullscreen-toggle" class="btn btn-outline-secondary btn-sm" title="Modo Tela Cheia">
                        <i class="fas fa-expand"></i> Tela Cheia
                    </button>
                    {% endif %}
                </div>
            </div>
            {% if capitulo.paginas.all %}
            <div id="manga-reader" class="mb-4">
                <div id="lazy-pages-container"></div>
                <div class="text-center my-3 d-none" id="lazy-loading-spinner">
                    <div class="spinner-border text-primary" role="status"><span class="visually-hidden">Carregando...</span></div>
                </div>
            </div>
            <div class="d-flex justify-content-between align-items-center mb-4">
                <button id="prev-page" class="btn btn-outline-primary" disabled><i class="fas fa-arrow-left"></i> Anterior</button>
                <span id="page-indicator">1 / {{ capitulo.paginas.count }}</span>
                <button id="next-page" class="btn btn-outline-primary">Próxima <i class="fas fa-arrow-right"></i></button>
            </div>
            
            <!-- Navegação entre capítulos -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                {% if capitulo_anterior %}
                <a href="{% url 'mangas:capitulo_detail' manga_slug=capitulo.manga.slug capitulo_slug=capitulo_anterior.slug %}" class="btn btn-outline-secondary">
                    <i class="fas fa-chevron-left"></i> Capítulo Anterior
                </a>
                {% else %}
                <div></div>
                {% endif %}
                
                <div class="text-center">
                    <small class="text-muted">
                        <i class="fas fa-keyboard"></i> Use as setas do teclado para navegar
                    </small>
                </div>
                
                {% if proximo_capitulo %}
                <a href="{% url 'mangas:capitulo_detail' manga_slug=capitulo.manga.slug capitulo_slug=proximo_capitulo.slug %}" class="btn btn-outline-secondary">
                    Próximo Capítulo <i class="fas fa-chevron-right"></i>
                </a>
                {% else %}
                <div></div>
                {% endif %}
            </div>
            {% else %}
            <div class="alert alert-info">Nenhuma página cadastrada para este capítulo.</div>
            {% endif %}
            <div class="d-flex flex-wrap gap-2">
                <div class="d-flex gap-2">
                    {% if capitulo_anterior %}
                    <a href="{% url 'mangas:capitulo_detail' manga_slug=capitulo.manga.slug capitulo_slug=capitulo_anterior.slug %}" class="btn btn-outline-primary btn-sm" title="Capítulo Anterior">
                        <i class="fas fa-arrow-left"></i> Anterior
                    </a>
                    {% endif %}
                    
                    <a href="{% url 'mangas:manga_detail' capitulo.manga.slug %}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-book"></i> Voltar ao mangá
                    </a>
                    
                    {% if capitulo.volume %}
                    <a href="{% url 'mangas:volume_detail' manga_slug=capitulo.manga.slug volume_slug=capitulo.volume.slug %}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-layer-group"></i> Ver Volume
                    </a>
                    {% endif %}
                    
                    {% if proximo_capitulo %}
                    <a href="{% url 'mangas:capitulo_detail' manga_slug=capitulo.manga.slug capitulo_slug=proximo_capitulo.slug %}" class="btn btn-outline-primary btn-sm" title="Próximo Capítulo">
                        Próximo <i class="fas fa-arrow-right"></i>
                    </a>
                    {% endif %}
                </div>
                
                {% if user|has_manga_permission %}
                <div class="d-flex gap-2 ms-auto">
                    <a href="{% url 'mangas:capitulo_edit' manga_slug=capitulo.manga.slug capitulo_slug=capitulo.slug %}" class="btn btn-outline-warning btn-sm">
                        <i class="fas fa-edit"></i> Editar
                    </a>
                    <a href="{% url 'mangas:pagina_create' capitulo.manga.slug capitulo.slug %}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i> Nova Página
                    </a>
                </div>
                {% endif %}
            </div>
            
            <!-- Navegação entre capítulos do volume -->
            {% if outros_capitulos_volume %}
            <div class="mt-4">
                <h5 class="h6 mb-2 text-muted">Outros capítulos deste volume:</h5>
                <div class="d-flex flex-wrap gap-2">
                    {% for cap in outros_capitulos_volume %}
                    <a href="{% url 'mangas:capitulo_detail' manga_slug=capitulo.manga.slug capitulo_slug=cap.slug %}" class="btn btn-sm {% if cap.id == capitulo.id %}btn-primary{% else %}btn-outline-primary{% endif %}" title="{% if cap.title %}{{ cap.title }}{% else %}Capítulo {{ cap.number }}{% endif %}">
                        {{ cap.number }}
                    </a>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
            
            <!-- Navegação entre volumes -->
            {% if volumes %}
            <div class="mt-4">
                <h5 class="h6 mb-2 text-muted">Outros volumes:</h5>
                <div class="d-flex flex-wrap gap-2">
                    {% for vol in volumes %}
                    <a href="{% url 'mangas:volume_detail' manga_slug=capitulo.manga.slug volume_slug=vol.slug %}" class="btn btn-sm btn-outline-secondary" title="Volume {{ vol.number }}{% if vol.title %}: {{ vol.title }}{% endif %}">
                        Vol. {{ vol.number }} ({{ vol.capitulo_count }})
                    </a>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
<!-- Container para o modo tela cheia -->
<div id="fullscreen-container" class="fullscreen-container">
    <div class="fullscreen-controls">
        <div class="chapter-info">
            {{ capitulo.manga.title }} - Capítulo {{ capitulo.number }}{% if capitulo.title %}: {{ capitulo.title }}{% endif %}
        </div>
        <div class="nav-buttons">
            <button id="fs-prev-page" title="Página Anterior">
                <i class="fas fa-arrow-left"></i>
            </button>
            <span class="page-info">
                <span id="fs-page-indicator">1 / {{ capitulo.paginas.count }}</span>
            </span>
            <button id="fs-next-page" title="Próxima Página">
                <i class="fas fa-arrow-right"></i>
            </button>
        </div>
        <button id="fs-close" class="close-btn" title="Sair do modo tela cheia">
            <i class="fas fa-times"></i>
        </button>
    </div>
    <div id="fullscreen-reader">
        <div id="fs-lazy-pages-container"></div>
        <div class="text-center my-3 d-none" id="fs-lazy-loading-spinner">
            <div class="spinner-border text-primary" role="status"><span class="visually-hidden">Carregando...</span></div>
        </div>
    </div>
</div>

{% if capitulo.paginas.count > 0 %}
<script>
const mangaSlug = "{{ capitulo.manga.slug }}";
const capituloSlug = "{{ capitulo.slug }}";
const totalPages = {{ capitulo.paginas.count }};
const perPage = 10;
let currentPage = 1;
let loadedPages = [];
let currentIndex = 0;

// URLs de navegação
{% if capitulo_anterior %}
const capituloAnteriorUrl = "{% url 'mangas:capitulo_detail' manga_slug=capitulo.manga.slug capitulo_slug=capitulo_anterior.slug %}";
{% else %}
const capituloAnteriorUrl = null;
{% endif %}
{% if proximo_capitulo %}
const proximoCapituloUrl = "{% url 'mangas:capitulo_detail' manga_slug=capitulo.manga.slug capitulo_slug=proximo_capitulo.slug %}";
{% else %}
const proximoCapituloUrl = null;
{% endif %}

function renderPages(pages) {
    const container = document.getElementById('lazy-pages-container');
    container.innerHTML = '';
    pages.forEach((p, idx) => {
        const div = document.createElement('div');
        div.className = 'manga-page mb-4 text-center';
        div.setAttribute('data-page', idx);
        div.style.display = idx === currentIndex ? 'block' : 'none';
        div.innerHTML = `<img src="${p.url}" class="img-fluid rounded shadow" alt="Página ${p.number}" style="max-height: 80vh;">
            <div class="small text-muted mt-2">Página ${p.number}</div>`;
        container.appendChild(div);
    });
}

function updatePageIndicator() {
    document.getElementById('page-indicator').textContent = (currentIndex+1) + ' / ' + totalPages;
}

function showSpinner(show) {
    document.getElementById('lazy-loading-spinner').classList.toggle('d-none', !show);
}

function loadPages(pageNum, cb) {
    showSpinner(true);
    fetch(`/mangas/${mangaSlug}/capitulo/${capituloSlug}/paginas-lazy/?page=${pageNum}&per_page=${perPage}`)
        .then(r => r.json())
        .then(data => {
            loadedPages = data.pages;
            currentIndex = 0;
            renderPages(loadedPages);
            updatePageIndicator();
            showSpinner(false);
            if (cb) cb(data);
        });
}

// Navegação com teclado
function handleKeyDown(e) {
    // Só processa se não estiver em um campo de input
    if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
        return;
    }
    
    switch (e.key) {
        case 'ArrowLeft':
            e.preventDefault();
            navigateToPreviousPage();
            break;
        case 'ArrowRight':
        case ' ':
            e.preventDefault();
            navigateToNextPage();
            break;
        case 'ArrowUp':
        case 'PageUp':
            e.preventDefault();
            navigateToPreviousPage();
            break;
        case 'ArrowDown':
        case 'PageDown':
        case 'Enter':
            e.preventDefault();
            navigateToNextPage();
            break;
        case 'Home':
            e.preventDefault();
            goToFirstPage();
            break;
        case 'End':
            e.preventDefault();
            goToLastPage();
            break;
        case 'f':
        case 'F':
            e.preventDefault();
            toggleFullscreen();
            break;
    }
}

function navigateToPreviousPage() {
    const prevBtn = document.getElementById('prev-page');
    if (!prevBtn.disabled) {
        prevBtn.click();
    } else if (capituloAnteriorUrl) {
        // Se não há página anterior, vai para o capítulo anterior
        window.location.href = capituloAnteriorUrl;
    }
}

function navigateToNextPage() {
    const nextBtn = document.getElementById('next-page');
    if (!nextBtn.disabled) {
        nextBtn.click();
    } else if (proximoCapituloUrl) {
        // Se não há próxima página, vai para o próximo capítulo
        window.location.href = proximoCapituloUrl;
    }
}

function goToFirstPage() {
    if (currentIndex > 0) {
        currentIndex = 0;
        renderPages(loadedPages);
        updatePageIndicator();
        updateNavigationButtons();
    }
}

function goToLastPage() {
    if (currentIndex < loadedPages.length - 1) {
        currentIndex = loadedPages.length - 1;
        renderPages(loadedPages);
        updatePageIndicator();
        updateNavigationButtons();
    }
}

function updateNavigationButtons() {
    const prevBtn = document.getElementById('prev-page');
    const nextBtn = document.getElementById('next-page');
    
    prevBtn.disabled = currentIndex === 0 && currentPage === 1;
    nextBtn.disabled = currentIndex === loadedPages.length - 1 && !hasMorePages();
}

function hasMorePages() {
    return currentPage * perPage < totalPages;
}

function toggleFullscreen() {
    const fullscreenToggle = document.getElementById('fullscreen-toggle');
    if (fullscreenToggle) {
        fullscreenToggle.click();
    }
}

document.addEventListener('DOMContentLoaded', function() {
    loadPages(currentPage);
    
    // Adiciona listener para teclado
    document.addEventListener('keydown', handleKeyDown);
    
    document.getElementById('prev-page').addEventListener('click', function() {
        if (currentIndex > 0) {
            document.querySelectorAll('.manga-page')[currentIndex].style.display = 'none';
            currentIndex--;
            document.querySelectorAll('.manga-page')[currentIndex].style.display = 'block';
            updatePageIndicator();
            updateNavigationButtons();
        } else if (currentPage > 1) {
            currentPage--;
            loadPages(currentPage, function() {
                updateNavigationButtons();
            });
        }
    });
    
    document.getElementById('next-page').addEventListener('click', function() {
        if (currentIndex < loadedPages.length - 1) {
            document.querySelectorAll('.manga-page')[currentIndex].style.display = 'none';
            currentIndex++;
            document.querySelectorAll('.manga-page')[currentIndex].style.display = 'block';
            updatePageIndicator();
            updateNavigationButtons();
        } else {
            // Carregar próximo lote se houver
            fetch(`/mangas/${mangaSlug}/capitulo/${capituloSlug}/paginas-lazy/?page=${currentPage+1}&per_page=${perPage}`)
                .then(r => r.json())
                .then(data => {
                    if (data.pages.length > 0) {
                        currentPage++;
                        loadedPages = data.pages;
                        currentIndex = 0;
                        renderPages(loadedPages);
                        updatePageIndicator();
                        updateNavigationButtons();
                    } else {
                        // Se não há mais páginas, vai para o próximo capítulo
                        if (proximoCapituloUrl) {
                            window.location.href = proximoCapituloUrl;
                        }
                    }
                });
        }
    });
    
    // Adiciona tooltip com atalhos de teclado
    const reader = document.getElementById('manga-reader');
    if (reader) {
        reader.title = 'Use as setas do teclado para navegar. F para tela cheia.';
    }
});

// Implementação do modo tela cheia
let fullscreenActive = false;
let hideControlsTimeout = null;

function renderFullscreenPages(pages) {
    const container = document.getElementById('fs-lazy-pages-container');
    if (!container) return;
    
    container.innerHTML = '';
    pages.forEach((p, idx) => {
        const div = document.createElement('div');
        div.className = 'fullscreen-page';
        div.setAttribute('data-page', idx);
        div.style.display = idx === currentIndex ? 'flex' : 'none';
        div.innerHTML = `
            <img src="${p.url}" alt="Página ${p.number}" style="max-width: 100%; max-height: 100vh; object-fit: contain;">
            <div class="page-number">Página ${p.number}</div>
        `;
        container.appendChild(div);
    });
}

function initFullscreen() {
    const fsContainer = document.getElementById('fullscreen-container');
    const fsToggle = document.getElementById('fullscreen-toggle');
    const fsClose = document.getElementById('fs-close');
    const fsPrev = document.getElementById('fs-prev-page');
    const fsNext = document.getElementById('fs-next-page');
    const fsPageIndicator = document.getElementById('fs-page-indicator');
    
    if (!fsContainer || !fsToggle) return;
    
    // Abrir modo tela cheia
    fsToggle.addEventListener('click', function() {
        openFullscreen();
    });
    
    // Fechar modo tela cheia
    if (fsClose) {
        fsClose.addEventListener('click', function() {
            closeFullscreen();
        });
    }
    
    // Navegação no modo tela cheia
    if (fsPrev) {
        fsPrev.addEventListener('click', function() {
            if (fullscreenActive) {
                navigateToPreviousPage();
            }
        });
    }
    
    if (fsNext) {
        fsNext.addEventListener('click', function() {
            if (fullscreenActive) {
                navigateToNextPage();
            }
        });
    }
    
    function openFullscreen() {
        fsContainer.style.display = 'block';
        document.body.style.overflow = 'hidden';
        fullscreenActive = true;
        
        // Renderiza as páginas no modo tela cheia
        if (loadedPages.length > 0) {
            renderFullscreenPages(loadedPages);
        }
        
        // Sincroniza com a página atual
        updateFullscreenPage();
        showFullscreenControls();
        
        // Adiciona listener específico para tela cheia
        document.addEventListener('keydown', handleFullscreenKeyDown);
    }
    
    function closeFullscreen() {
        fsContainer.style.display = 'none';
        document.body.style.overflow = '';
        fullscreenActive = false;
        
        if (hideControlsTimeout) {
            clearTimeout(hideControlsTimeout);
        }
        
        document.removeEventListener('keydown', handleFullscreenKeyDown);
    }
    
    function handleFullscreenKeyDown(e) {
        if (!fullscreenActive) return;
        
        switch (e.key) {
            case 'Escape':
                closeFullscreen();
                break;
            case 'ArrowLeft':
                e.preventDefault();
                navigateToPreviousPage();
                break;
            case 'ArrowRight':
            case ' ':
                e.preventDefault();
                navigateToNextPage();
                break;
        }
    }
    
    function updateFullscreenPage() {
        if (fsPageIndicator) {
            fsPageIndicator.textContent = (currentIndex + 1) + ' / ' + totalPages;
        }
        
        // Atualiza os botões
        if (fsPrev) {
            fsPrev.disabled = currentIndex === 0 && currentPage === 1;
        }
        if (fsNext) {
            fsNext.disabled = currentIndex === loadedPages.length - 1 && !hasMorePages();
        }
        
        // Atualiza a exibição das páginas no modo tela cheia
        if (fullscreenActive && loadedPages.length > 0) {
            const fsPages = document.querySelectorAll('.fullscreen-page');
            fsPages.forEach((page, idx) => {
                page.style.display = idx === currentIndex ? 'flex' : 'none';
            });
        }
    }
    
    function showFullscreenControls() {
        const controls = document.querySelector('.fullscreen-controls');
        if (controls) {
            controls.style.display = 'flex';
            
            if (hideControlsTimeout) {
                clearTimeout(hideControlsTimeout);
            }
            
            hideControlsTimeout = setTimeout(() => {
                if (controls && fullscreenActive) {
                    controls.style.display = 'none';
                }
            }, 3000);
        }
    }
    
    // Mostra controles ao mover o mouse
    fsContainer.addEventListener('mousemove', function() {
        if (fullscreenActive) {
            showFullscreenControls();
        }
    });
    
    // Clique na área para navegar
    fsContainer.addEventListener('click', function(e) {
        if (!fullscreenActive) return;
        
        const rect = fsContainer.getBoundingClientRect();
        const clickX = e.clientX - rect.left;
        const third = rect.width / 3;
        
        if (clickX < third) {
            navigateToPreviousPage();
        } else if (clickX > third * 2) {
            navigateToNextPage();
        } else {
            // Clique no meio - mostra/oculta controles
            const controls = document.querySelector('.fullscreen-controls');
            if (controls) {
                if (controls.style.display === 'flex') {
                    controls.style.display = 'none';
                } else {
                    showFullscreenControls();
                }
            }
        }
    });
    
    // Atualiza a função para sincronizar com o modo tela cheia
    const originalUpdatePageIndicator = updatePageIndicator;
    updatePageIndicator = function() {
        originalUpdatePageIndicator();
        if (fullscreenActive) {
            updateFullscreenPage();
        }
    };
    
    // Atualiza a função renderPages para também renderizar no modo tela cheia
    const originalRenderPages = renderPages;
    renderPages = function(pages) {
        originalRenderPages(pages);
        if (fullscreenActive) {
            renderFullscreenPages(pages);
        }
    };
}

// Inicializa o modo tela cheia
document.addEventListener('DOMContentLoaded', function() {
    initFullscreen();
});
</script>
{% endif %}
{% endblock %}
{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar fixo à esquerda, sempre visível em md+ -->
        <div class="offcanvas-md offcanvas-start offcanvas-fullscreen" tabindex="-1" id="sidebar" aria-labelledby="sidebarLabel">
            <div class="offcanvas-header d-md-none">
                <h5 class="offcanvas-title" id="sidebarLabel">Menu de Configurações</h5>
                <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#sidebar" aria-label="Close"></button>
            </div>
            <div class="offcanvas-body p-0">
                {% include 'config/includes/sidebar.html' %}
            </div>
        </div>
        <!-- Conteúdo principal alinhado à direita do sidebar -->
        <main class="col-12 px-2 px-md-4 pt-4 pb-4" id="config-main-content">
            <!-- Bo<PERSON><PERSON> hamburguer para mobile -->
            <button class="btn btn-outline-primary d-md-none my-2" type="button" data-bs-toggle="offcanvas" data-bs-target="#sidebar" aria-controls="sidebar">
                <i class="fas fa-bars"></i> Menu
            </button>
            <div class="config-content">
                {% block config_content %}
                <!-- Content goes here -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2 config-page-title">
                        <i class="fas fa-cog me-2" style="color: var(--nix-accent);"></i>Painel de Configurações
                    </h1>
                </div>
                <p class="lead">Sistema de administração do Project Nix</p>
                {% endblock %}
            </div>
        </main>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
@media (max-width: 767.98px) {
  .offcanvas-fullscreen {
    width: 100vw !important;
    max-width: 100vw !important;
    height: 100vh !important;
    max-height: 100vh !important;
    border-radius: 0 !important;
  }
  .offcanvas-fullscreen .offcanvas-body {
    padding-bottom: 2rem;
    overflow-y: auto;
  }
}
</style>
{% endblock %}

{% block extra_js %}{% endblock %}

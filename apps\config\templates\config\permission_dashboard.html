{% extends 'base.html' %}
{% load static %}

{% block title %}Dash<PERSON> <PERSON> Permissões - Project Nix{% endblock %}

{% block extra_css %}
<style>
    .dashboard-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        padding: 25px;
        color: white;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }
    
    .dashboard-card:hover {
        transform: translateY(-5px);
    }
    
    .dashboard-card h3 {
        font-size: 1.1rem;
        margin-bottom: 10px;
        opacity: 0.9;
    }
    
    .dashboard-card .number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .dashboard-card .change {
        font-size: 0.9rem;
        opacity: 0.8;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .chart-container {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        margin-bottom: 20px;
    }
    
    .chart-title {
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 20px;
        color: #333;
    }
    
    .activity-list {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
    
    .activity-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 0;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .activity-item:last-child {
        border-bottom: none;
    }
    
    .activity-info h4 {
        margin: 0;
        font-size: 1rem;
        color: #333;
    }
    
    .activity-info p {
        margin: 5px 0 0 0;
        color: #666;
        font-size: 0.9rem;
    }
    
    .activity-status {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .status-success {
        background: #d4edda;
        color: #155724;
    }
    
    .status-warning {
        background: #fff3cd;
        color: #856404;
    }
    
    .status-danger {
        background: #f8d7da;
        color: #721c24;
    }
    
    .quick-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 30px;
    }
    
    .action-btn {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 20px;
        text-align: center;
        text-decoration: none;
        color: #333;
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    
    .action-btn:hover {
        border-color: #667eea;
        color: #667eea;
        text-decoration: none;
        transform: translateY(-2px);
    }
    
    .action-btn i {
        font-size: 2rem;
        margin-bottom: 10px;
        color: #667eea;
    }
    
    .cache-stats {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
    
    .cache-stat-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .cache-stat-item:last-child {
        border-bottom: none;
    }
    
    .cache-stat-label {
        font-weight: 500;
        color: #333;
    }
    
    .cache-stat-value {
        font-weight: 600;
        color: #667eea;
    }
    
    @media (max-width: 768px) {
        .stats-grid {
            grid-template-columns: 1fr;
        }
        
        .quick-actions {
            grid-template-columns: 1fr;
        }
        
        .dashboard-card .number {
            font-size: 2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h2 mb-0">
                <i class="fas fa-shield-alt text-primary"></i>
                Dashboard de Permissões
            </h1>
            <p class="text-muted">Monitoramento e gerenciamento do sistema de permissões</p>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
        <a href="{% url 'config:user_permissions' %}" class="action-btn">
            <i class="fas fa-users"></i>
            <span>Gerenciar Usuários</span>
        </a>
        <a href="{% url 'config:group_permissions' %}" class="action-btn">
            <i class="fas fa-layer-group"></i>
            <span>Gerenciar Grupos</span>
        </a>
        <a href="{% url 'config:cache_management' %}" class="action-btn">
            <i class="fas fa-database"></i>
            <span>Gerenciar Cache</span>
        </a>
        <a href="{% url 'config:permission_analytics' %}" class="action-btn">
            <i class="fas fa-chart-line"></i>
            <span>Analytics</span>
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-grid">
        <div class="dashboard-card">
            <h3><i class="fas fa-users"></i> Total de Usuários</h3>
            <div class="number">{{ total_users }}</div>
            <div class="change">
                <i class="fas fa-arrow-up"></i> +{{ active_users }} ativos
            </div>
        </div>
        
        <div class="dashboard-card">
            <h3><i class="fas fa-layer-group"></i> Grupos</h3>
            <div class="number">{{ total_groups }}</div>
            <div class="change">
                <i class="fas fa-users"></i> {{ group_stats|length }} com membros
            </div>
        </div>
        
        <div class="dashboard-card">
            <h3><i class="fas fa-user-shield"></i> Staff</h3>
            <div class="number">{{ staff_users }}</div>
            <div class="change">
                <i class="fas fa-crown"></i> {{ superusers }} superusuários
            </div>
        </div>
        
        <div class="dashboard-card">
            <h3><i class="fas fa-tachometer-alt"></i> Performance</h3>
            <div class="number">{{ permission_activity.cache_hit_rate|floatformat:1 }}%</div>
            <div class="change">
                <i class="fas fa-clock"></i> {{ permission_activity.avg_response_time|floatformat:3 }}s
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Cache Statistics -->
        <div class="col-lg-4">
            <div class="cache-stats">
                <h4 class="chart-title">
                    <i class="fas fa-database text-primary"></i>
                    Estatísticas do Cache
                </h4>
                
                <div class="cache-stat-item">
                    <span class="cache-stat-label">Backend</span>
                    <span class="cache-stat-value">{{ cache_stats.backend|default:"Unknown" }}</span>
                </div>
                
                <div class="cache-stat-item">
                    <span class="cache-stat-label">TTL Padrão</span>
                    <span class="cache-stat-value">{{ cache_stats.default_ttl|default:300 }}s</span>
                </div>
                
                <div class="cache-stat-item">
                    <span class="cache-stat-label">Prefixos</span>
                    <span class="cache-stat-value">{{ cache_stats.prefixes|length }}</span>
                </div>
                
                <div class="cache-stat-item">
                    <span class="cache-stat-label">Hit Rate</span>
                    <span class="cache-stat-value">{{ permission_activity.cache_hit_rate|floatformat:1 }}%</span>
                </div>
            </div>
        </div>

        <!-- Recent Users -->
        <div class="col-lg-8">
            <div class="activity-list">
                <h4 class="chart-title">
                    <i class="fas fa-user-plus text-primary"></i>
                    Usuários Recentes
                </h4>
                
                {% for user in recent_users %}
                <div class="activity-item">
                    <div class="activity-info">
                        <h4>{{ user.username }}</h4>
                        <p>{{ user.email }} • Registrado em {{ user.date_joined|date:"d/m/Y H:i" }}</p>
                    </div>
                    <div class="activity-status {% if user.is_active %}status-success{% else %}status-warning{% endif %}">
                        {% if user.is_active %}Ativo{% else %}Inativo{% endif %}
                    </div>
                </div>
                {% empty %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-users fa-3x mb-3"></i>
                    <p>Nenhum usuário recente</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Permission Activity -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="chart-container">
                <h4 class="chart-title">
                    <i class="fas fa-chart-bar text-primary"></i>
                    Atividade de Permissões (Hoje)
                </h4>
                
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h3 text-primary">{{ permission_activity.total_checks_today }}</div>
                            <small class="text-muted">Verificações</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h3 text-success">{{ permission_activity.total_checks_today|add:"-"|add:permission_activity.denied_access_today }}</div>
                            <small class="text-muted">Permitidas</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h3 text-danger">{{ permission_activity.denied_access_today }}</div>
                            <small class="text-muted">Negadas</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h3 text-info">{{ permission_activity.avg_response_time|floatformat:3 }}s</div>
                            <small class="text-muted">Tempo Médio</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Groups -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="chart-container">
                <h4 class="chart-title">
                    <i class="fas fa-layer-group text-primary"></i>
                    Grupos Mais Populares
                </h4>
                
                <div class="row">
                    {% for group in group_stats %}
                    <div class="col-md-4 mb-3">
                        <div class="d-flex justify-content-between align-items-center p-3 bg-light rounded">
                            <div>
                                <strong>{{ group.name }}</strong>
                                <br>
                                <small class="text-muted">{{ group.user_count }} membros</small>
                            </div>
                            <div class="text-primary">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="col-12 text-center text-muted py-4">
                        <i class="fas fa-layer-group fa-3x mb-3"></i>
                        <p>Nenhum grupo encontrado</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-refresh das estatísticas a cada 30 segundos
setInterval(function() {
    // Aqui você pode adicionar AJAX para atualizar as estatísticas
    console.log('Atualizando estatísticas...');
}, 30000);

// Animações suaves
document.addEventListener('DOMContentLoaded', function() {
    // Anima os cards ao carregar
    const cards = document.querySelectorAll('.dashboard-card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'all 0.5s ease';
            
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        }, index * 100);
    });
});
</script>
{% endblock %} 
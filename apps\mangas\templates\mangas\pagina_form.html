{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}{% if form.instance.pk %}Editar Página{% else %}Adicionar <PERSON>gin<PERSON>{% endif %} - Project Nix{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h2 class="mb-0">
                        {% if form.instance.pk %}
                            Editar Página
                        {% else %}
                            Adicionar Nova Página
                        {% endif %}
                    </h2>
                </div>
                <div class="card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                    
                    <form method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.number.id_for_label }}" class="form-label">Número da Página</label>
                            <input type="number" 
                                   name="{{ form.number.name }}" 
                                   class="form-control {% if form.number.errors %}is-invalid{% endif %}" 
                                   id="{{ form.number.id_for_label }}" 
                                   value="{{ form.number.value|default:'' }}" 
                                   required>
                            {% if form.number.help_text %}
                                <div class="form-text">{{ form.number.help_text }}</div>
                            {% endif %}
                            {% if form.number.errors %}
                                <div class="invalid-feedback">
                                    {{ form.number.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.image.id_for_label }}" class="form-label">Imagem da Página</label>
                            <input type="file" 
                                   name="{{ form.image.name }}" 
                                   class="form-control {% if form.image.errors %}is-invalid{% endif %}" 
                                   id="{{ form.image.id_for_label }}"
                                   accept="image/*"
                                   {% if not form.instance.pk %}required{% endif %}>
                            <div class="form-text">
                                Formatos suportados: JPG, PNG, WEBP, GIF. Tamanho máximo: 10MB.
                            </div>
                            {% if form.image.errors %}
                                <div class="invalid-feedback">
                                    {{ form.image.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ view.get_success_url }}" class="btn btn-secondary me-md-2">
                                <i class="fas fa-arrow-left"></i> Voltar
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Salvar
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Adiciona classe de validação ao formulário
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.querySelector('form');
        
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        }, false);
    });
</script>
{% endblock %}

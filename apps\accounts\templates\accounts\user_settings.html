{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}Configurações - {{ block.super }}{% endblock %}

{% block content %}
<!-- Breadcrumb personalizado -->
<nav aria-label="breadcrumb" class="bg-secondary border-bottom">
    <div class="container">
        <ol class="breadcrumb mb-0 py-3">
            <li class="breadcrumb-item">
                <a href="{% url 'pages:home' %}" class="text-decoration-none">
                    <i class="fas fa-home me-1"></i>Home
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{% url 'accounts:profile' %}" class="text-decoration-none">
                    <i class="fas fa-user me-1"></i>Perfil
                </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
                <i class="fas fa-cog me-1"></i>Configurações
            </li>
        </ol>
    </div>
</nav>

<div class="container my-5">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-1 text-sans text-body">
                        <i class="fas fa-cog me-2 text-django-green"></i>Configurações da Conta
                    </h1>
                    <p class="text-secondary mb-0 text-body">Gerencie suas informações pessoais e configurações de segurança</p>
                </div>
                <div>
                    <a href="{% url 'accounts:profile' %}" class="btn btn-outline-primary">
                        <i class="fas fa-user me-1"></i>Ver Perfil
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Sidebar de Navegação -->
        <div class="col-lg-3">
            <div class="card-django border-0 shadow-sm mb-4">
                <div class="card-header bg-django-green text-light card-django">
                    <h6 class="mb-0 text-sans text-body">
                        <i class="fas fa-sliders-h me-2"></i>Configurações
                    </h6>
                </div>
                <div class="card-body p-0 card-django">
                    <div class="list-group list-group-flush">
                        <a href="#profile-section" class="list-group-item list-group-item-action active" data-section="profile">
                            <i class="fas fa-user me-2 text-django-green"></i>
                            <span>Informações Pessoais</span>
                            <small class="d-block text-secondary">Nome, bio, telefone</small>
                        </a>
                        <a href="#avatar-section" class="list-group-item list-group-item-action" data-section="avatar">
                            <i class="fas fa-camera me-2 text-success"></i>
                            <span>Foto de Perfil</span>
                            <small class="d-block text-secondary">Upload e gerenciar avatar</small>
                        </a>
                        <a href="#email-section" class="list-group-item list-group-item-action" data-section="email">
                            <i class="fas fa-envelope me-2 text-warning"></i>
                            <span>E-mail</span>
                            <small class="d-block text-secondary">Alterar endereço de e-mail</small>
                        </a>
                        <a href="#password-section" class="list-group-item list-group-item-action" data-section="password">
                            <i class="fas fa-lock me-2 text-danger"></i>
                            <span>Senha</span>
                            <small class="d-block text-secondary">Alterar senha de acesso</small>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Card de Informações do Usuário -->
            <div class="card-django border-0 shadow-sm">
                <div class="card-body text-center card-django">
                    <img src="{{ profile_user.get_avatar_url }}"
                         alt="Avatar de {{ profile_user.get_full_name|default:profile_user.username }}"
                         class="rounded-circle mb-3 avatar-lg">
                    <h6 class="mb-1 text-sans text-body">{{ profile_user.get_full_name|default:profile_user.username }}</h6>
                    <p class="text-secondary small mb-0 text-body">{{ profile_user.email }}</p>
                </div>
            </div>
        </div>

        <!-- Conteúdo Principal -->
        <div class="col-lg-9">
            <!-- Seção de Perfil -->
            <div id="profile-section" class="settings-section">
                <div class="card-django border-0 shadow-sm mb-4">
                    <div class="card-header bg-secondary border-0 card-django">
                        <h5 class="mb-0 text-sans text-body">
                            <i class="fas fa-user me-2"></i>Informações Pessoais
                        </h5>
                    </div>
                    <div class="card-body card-django">
                        <form method="post" class="needs-validation form-django" novalidate aria-label="Formulário de informações pessoais" role="form">
                            {% csrf_token %}
                            <input type="hidden" name="form_type" value="profile">
                            {% crispy profile_form %}
                        </form>
                    </div>
                </div>
            </div>

            <!-- Seção de Avatar -->
            <div id="avatar-section" class="settings-section d-none">
                <div class="card-django border-0 shadow-sm mb-4">
                    <div class="card-header bg-secondary border-0 card-django">
                        <h5 class="mb-0 text-sans text-body">
                            <i class="fas fa-camera me-2"></i>Foto de Perfil
                        </h5>
                    </div>
                    <div class="card-body card-django">
                        <div class="row">
                            <div class="col-md-4 text-center">
                                <div class="mb-3">
                                    <img src="{{ profile_user.get_avatar_url }}"
                                         alt="Avatar atual de {{ profile_user.get_full_name|default:profile_user.username }}"
                                         class="rounded-circle border border-3 border-secondary shadow avatar-xl"
                                         id="avatar-preview">
                                </div>
                                {% if profile_user.avatar %}
                                    <form method="post" action="{% url 'accounts:remove_avatar' %}" class="d-inline form-django" aria-label="Formulário de remoção de avatar" role="form">
                                        {% csrf_token %}
                                        <button type="submit" class="btn btn-outline-danger btn-sm text-sans" 
                                                onclick="return confirm('Tem certeza que deseja remover sua foto de perfil?')">
                                            <i class="fas fa-trash me-1"></i>Remover Foto
                                        </button>
                                    </form>
                                {% endif %}
                            </div>
                            <div class="col-md-8 mb-3">
                                <form method="post" enctype="multipart/form-data" class="needs-validation form-django" novalidate aria-label="Formulário de upload de avatar" role="form">
                                    {% csrf_token %}
                                    <input type="hidden" name="form_type" value="avatar">
                                    
                                    <div class="mb-3">
                                        <label for="id_avatar" class="form-label text-sans">Nova Foto de Perfil</label>
                                        <input type="file" class="form-control" id="id_avatar" name="avatar" accept="image/*" onchange="previewAvatar(this)">
                                        <div class="form-text">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Formatos aceitos: JPG, PNG, GIF, WebP. Tamanho máximo: 5MB
                                        </div>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-success text-sans">
                                        <i class="fas fa-upload me-2"></i>Atualizar Foto
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Seção de E-mail -->
            <div id="email-section" class="settings-section d-none">
                <div class="card-django border-0 shadow-sm mb-4">
                    <div class="card-header bg-secondary border-0 card-django">
                        <h5 class="mb-0 text-sans text-body">
                            <i class="fas fa-envelope me-2"></i>Alterar E-mail
                        </h5>
                    </div>
                    <div class="card-body card-django">
                        <form method="post" class="needs-validation form-django" novalidate aria-label="Formulário de alteração de email" role="form">
                            {% csrf_token %}
                            <input type="hidden" name="form_type" value="email">
                            {% crispy email_form %}
                        </form>
                    </div>
                </div>
            </div>

            <!-- Seção de Senha -->
            <div id="password-section" class="settings-section d-none">
                <div class="card-django border-0 shadow-sm mb-4">
                    <div class="card-header bg-secondary border-0 card-django">
                        <h5 class="mb-0 text-sans text-body">
                            <i class="fas fa-lock me-2"></i>Alterar Senha
                        </h5>
                    </div>
                    <div class="card-body card-django">
                        <form method="post" class="needs-validation form-django" novalidate aria-label="Formulário de alteração de senha" role="form">
                            {% csrf_token %}
                            <input type="hidden" name="form_type" value="password">
                            {% crispy password_form %}
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.settings-section {
    transition: opacity 0.3s ease;
}

.list-group-item {
    border: none;
    padding: 16px 20px;
    transition: all 0.2s ease;
    cursor: pointer;
}

.list-group-item:hover {
    background-color: #f8f9fa;
    transform: translateX(3px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.list-group-item.active {
    background-color: #e8f5e8;
    color: var(--bs-django-green);
    border-left: 4px solid var(--bs-django-green);
    font-weight: 500;
}

.list-group-item.active:hover {
    background-color: #e8f5e8;
    transform: none;
}

.list-group-item.active i {
    color: var(--bs-django-green) !important;
}

.list-group-item.active small {
    color: var(--bs-django-green) !important;
    opacity: 0.8;
}

.card-django {
    transition: transform 0.2s ease;
}

.card-django:hover {
    transform: translateY(-2px);
}

#avatar-preview {
    transition: transform 0.3s ease;
}

#avatar-preview:hover {
    transform: scale(1.05);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Navegação entre seções
    const navLinks = document.querySelectorAll('[data-section]');
    const sections = document.querySelectorAll('.settings-section');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active de todos os links
            navLinks.forEach(l => l.classList.remove('active'));
            
            // Adiciona active ao link clicado
            this.classList.add('active');
            
            // Esconde todas as seções
            sections.forEach(section => section.classList.add('d-none'));
            
            // Mostra a seção correspondente
            const targetSection = document.getElementById(this.dataset.section + '-section');
            if (targetSection) {
                targetSection.classList.remove('d-none');
            }
        });
    });
    
    // Verificar se há hash na URL
    const hash = window.location.hash;
    if (hash) {
        const targetLink = document.querySelector(`[href="${hash}"]`);
        if (targetLink) {
            targetLink.click();
        }
    }
});

function previewAvatar(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            document.getElementById('avatar-preview').src = e.target.result;
        }
        
        reader.readAsDataURL(input.files[0]);
    }
}

// Validação de formulários
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                    showErrorToast('❌ Por favor, corrija os erros no formulário antes de continuar.');
                } else {
                    // Mostrar toast de carregamento
                    showInfoToast('⏳ Processando suas alterações...');
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Validações em tempo real
document.addEventListener('DOMContentLoaded', function() {
    // Validação de telefone
    const phoneInput = document.getElementById('id_phone');
    if (phoneInput) {
        phoneInput.addEventListener('input', function() {
            let value = this.value.replace(/\D/g, '');
            if (value.length >= 10) {
                if (value.length === 11) {
                    this.value = `(${value.slice(0,2)}) ${value.slice(2,7)}-${value.slice(7)}`;
                } else if (value.length === 10) {
                    this.value = `(${value.slice(0,2)}) ${value.slice(2,6)}-${value.slice(6)}`;
                }
            }
        });
    }

    // Validação de nome em tempo real
    const firstNameInput = document.getElementById('id_first_name');
    if (firstNameInput) {
        firstNameInput.addEventListener('input', function() {
            const value = this.value;
            const isValid = /^[a-zA-ZÀ-ÿ\s]+$/.test(value) && value.length >= 2;

            if (value && !isValid) {
                this.setCustomValidity('Nome deve conter apenas letras e ter pelo menos 2 caracteres');
            } else {
                this.setCustomValidity('');
            }
        });
    }

    // Validação de sobrenome em tempo real
    const lastNameInput = document.getElementById('id_last_name');
    if (lastNameInput) {
        lastNameInput.addEventListener('input', function() {
            const value = this.value;
            const isValid = /^[a-zA-ZÀ-ÿ\s]+$/.test(value) && value.length >= 2;

            if (value && !isValid) {
                this.setCustomValidity('Sobrenome deve conter apenas letras e ter pelo menos 2 caracteres');
            } else {
                this.setCustomValidity('');
            }
        });
    }

    // Validação de email em tempo real
    const emailInput = document.getElementById('id_email');
    if (emailInput) {
        emailInput.addEventListener('input', function() {
            const value = this.value;
            const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

            if (value && !emailRegex.test(value)) {
                this.setCustomValidity('Por favor, insira um e-mail válido');
            } else {
                this.setCustomValidity('');
            }
        });
    }

    // Validação de senha em tempo real
    const newPasswordInput = document.getElementById('id_new_password1');
    if (newPasswordInput) {
        newPasswordInput.addEventListener('input', function() {
            const value = this.value;
            const hasLetter = /[a-zA-Z]/.test(value);
            const hasNumber = /\d/.test(value);
            const isLongEnough = value.length >= 8;

            if (value && (!hasLetter || !hasNumber || !isLongEnough)) {
                this.setCustomValidity('Senha deve ter pelo menos 8 caracteres, incluindo letras e números');
            } else {
                this.setCustomValidity('');
            }
        });
    }

    // Validação de confirmação de senha
    const confirmPasswordInput = document.getElementById('id_new_password2');
    if (confirmPasswordInput && newPasswordInput) {
        confirmPasswordInput.addEventListener('input', function() {
            if (this.value !== newPasswordInput.value) {
                this.setCustomValidity('As senhas não coincidem');
            } else {
                this.setCustomValidity('');
            }
        });
    }

    // Contador de caracteres para bio
    const bioInput = document.getElementById('id_bio');
    if (bioInput) {
        const maxLength = 500;
        const counter = document.createElement('small');
        counter.className = 'form-text text-secondary';
        bioInput.parentNode.appendChild(counter);

        function updateCounter() {
            const remaining = maxLength - bioInput.value.length;
            counter.textContent = `${remaining} caracteres restantes`;

            if (remaining < 50) {
                counter.className = 'form-text text-warning';
            } else if (remaining < 0) {
                counter.className = 'form-text text-danger';
            } else {
                counter.className = 'form-text text-secondary';
            }
        }

        bioInput.addEventListener('input', updateCounter);
        updateCounter();
    }
});
</script>
{% endblock %}

{% extends 'config/base_config.html' %}

{% block config_content %}
<!-- <PERSON><PERSON><PERSON><PERSON> do backup de mídia -->
<div class="container my-5">
    <h1 class="h3 mb-4">Backup de Mídia</h1>
    {% if messages %}
      {% for message in messages %}
        <div class="alert alert-{{ message.tags }}">{{ message }}</div>
      {% endfor %}
    {% endif %}
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Arquivo</th>
                <th>Tamanho</th>
                <th>Data</th>
                <th>Ações</th>
            </tr>
        </thead>
        <tbody>
            {% for backup in backups %}
            <tr>
                <td>{{ backup.name }}</td>
                <td>{{ backup.size|filesizeformat }}</td>
                <td>{{ backup.modified|date:'d/m/Y H:i' }}</td>
                <td>
                    <a href="{% url 'config:download_backup' 'media' backup.name %}" class="btn btn-sm btn-outline-success">Download</a>
                    <form method="post" action="{% url 'config:delete_backup' 'media' backup.name %}" style="display:inline;" onsubmit="return confirm('Tem certeza que deseja excluir este backup?');">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-sm btn-outline-danger">Excluir</button>
                    </form>
                </td>
            </tr>
            {% empty %}
            <tr><td colspan="4" class="text-center text-muted">Nenhum backup encontrado.</td></tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %} 
{% extends 'config/base_config.html' %}

{% block config_content %}
<!-- Listagem de usuários -->
<div class="container my-5">
    <h1 class="h3 mb-4">Usuários do Sistema</h1>
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Email</th>
                <th>Nome</th>
                <th>Username</th>
                <th>Status</th>
                <th>Ações</th>
            </tr>
        </thead>
        <tbody>
            {% for user in users %}
            <tr>
                <td>{{ user.email }}</td>
                <td>{{ user.get_full_name|default:user.username }}</td>
                <td>{{ user.username }}</td>
                <td>
                    {% if user.is_active %}<span class="badge bg-success">Ativo</span>{% else %}<span class="badge bg-danger">Inativo</span>{% endif %}
                    {% if user.is_staff %}<span class="badge bg-info">Staff</span>{% endif %}
                    {% if user.is_superuser %}<span class="badge bg-warning text-dark">Superuser</span>{% endif %}
                </td>
                <td>
                    <a href="{% url 'config:user_update' user.slug %}" class="btn btn-sm btn-outline-primary">Editar</a>
                    <a href="{% url 'config:user_delete' user.slug %}" class="btn btn-sm btn-outline-danger">Excluir</a>
                </td>
            </tr>
            {% empty %}
            <tr><td colspan="5" class="text-center text-muted">Nenhum usuário encontrado.</td></tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}
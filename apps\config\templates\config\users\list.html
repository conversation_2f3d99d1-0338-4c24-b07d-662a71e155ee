{% extends 'config/base_config_page.html' %}
{% load config_extras %}

{% block config_title %}Gerenciar Usuários{% endblock %}
{% block page_icon %}<i class="fas fa-users me-2"></i>{% endblock %}
{% block page_title %}Gerenciar Usuários{% endblock %}
{% block page_actions %}
<a href="{% url 'config:user_create' %}" class="btn btn-primary">
    <i class="fas fa-user-plus me-2"></i>Novo Usuário
</a>
{% endblock %}

{% block page_content %}
<div class="card shadow-sm">
    <div class="card-body p-0">
        <div class="table-responsive d-none d-md-block">
            <table class="table table-hover align-middle mb-0">
                <thead class="table-theme-light">
                    <tr>
                        <th scope="col"><i class="fas fa-user"></i> Nome</th>
                        <th scope="col"><i class="fas fa-envelope"></i> Email</th>
                        <th scope="col"><i class="fas fa-calendar"></i> Data de Registro</th>
                        <th scope="col"><i class="fas fa-shield-alt"></i> Status</th>
                        <th scope="col"><i class="fas fa-cogs"></i> Ações</th>
                    </tr>
                </thead>
                <tbody>
                {% for user in users %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm me-3">
                                    <div class="bg-django-green rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                        <i class="fas fa-user text-white"></i>
                                    </div>
                                </div>
                                <div>
                                    <div class="fw-bold">{{ user.get_full_name|default:user.username }}</div>
                                    <small class="text-muted">@{{ user.username }}</small>
                                </div>
                            </div>
                        </td>
                        <td>{{ user.email }}</td>
                        <td>{{ user.date_joined|date:"d/m/Y H:i" }}</td>
                        <td>
                            {% if user.is_active %}
                                <span class="badge bg-theme-success"><i class="fas fa-check-circle"></i> Ativo</span>
                            {% else %}
                                <span class="badge bg-danger"><i class="fas fa-times-circle"></i> Inativo</span>
                            {% endif %}
                            {% if user.is_staff %}
                                <span class="badge bg-warning ms-1"><i class="fas fa-user-tie"></i> Staff</span>
                            {% endif %}
                        </td>
                        <td>
                                                    <div class="btn-group btn-group-sm" role="group">
                            <a href="{% url 'accounts:profile_with_slug' user.slug %}" class="btn btn-outline-info" title="Ver Perfil">
                                <i class="fas fa-user"></i>
                            </a>
                            <a href="{% url 'config:user_update' user.slug %}" class="btn btn-outline-primary" title="Editar">
                                <i class="fas fa-edit"></i>
                            </a>
                            {% if user.is_active %}
                                <form method="post" action="{% url 'config:user_deactivate' user.slug %}" class="d-inline" onsubmit="return confirm('Tem certeza que deseja desativar este usuário?')">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-outline-warning" title="Desativar">
                                        <i class="fas fa-pause"></i>
                                    </button>
                                </form>
                            {% else %}
                                <form method="post" action="{% url 'config:user_activate' user.slug %}" class="d-inline" onsubmit="return confirm('Tem certeza que deseja ativar este usuário?')">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-outline-success" title="Ativar">
                                        <i class="fas fa-play"></i>
                                    </button>
                                </form>
                            {% endif %}
                            <a href="{% url 'config:user_delete' user.slug %}" class="btn btn-outline-danger" title="Deletar">
                                <i class="fas fa-trash"></i>
                            </a>
                        </div>
                        </td>
                    </tr>
                {% empty %}
                    <tr><td colspan="5" class="text-center text-muted">Nenhum usuário encontrado.</td></tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Layout responsivo para mobile -->
        <div class="d-block d-md-none">
            {% for user in users %}
            <div class="card mb-2">
                <div class="card-body py-2 px-3">
                    <div class="d-flex align-items-center mb-2">
                        <div class="avatar-sm me-3">
                            <div class="bg-django-green rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                <i class="fas fa-user text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <div class="fw-bold">{{ user.get_full_name|default:user.username }}</div>
                            <small class="text-muted">@{{ user.username }}</small>
                        </div>
                    </div>
                    <div class="mb-2">
                        <div class="small text-muted">{{ user.email }}</div>
                        <div class="small text-muted">Registrado em: {{ user.date_joined|date:"d/m/Y" }}</div>
                    </div>
                    <div class="mb-2">
                        {% if user.is_active %}
                            <span class="badge bg-theme-success"><i class="fas fa-check-circle"></i> Ativo</span>
                        {% else %}
                            <span class="badge bg-danger"><i class="fas fa-times-circle"></i> Inativo</span>
                        {% endif %}
                        {% if user.is_staff %}
                            <span class="badge bg-warning ms-1"><i class="fas fa-user-tie"></i> Staff</span>
                        {% endif %}
                    </div>
                    <div class="btn-group btn-group-sm w-100" role="group">
                        <a href="{% url 'accounts:profile_with_slug' user.slug %}" class="btn btn-outline-info w-100" title="Ver Perfil">
                            <i class="fas fa-user"></i>
                        </a>
                        <a href="{% url 'config:user_update' user.slug %}" class="btn btn-outline-primary w-100" title="Editar">
                            <i class="fas fa-edit"></i>
                        </a>
                        {% if user.is_active %}
                            <form method="post" action="{% url 'config:user_deactivate' user.slug %}" class="d-inline w-100" onsubmit="return confirm('Tem certeza que deseja desativar este usuário?')">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-outline-warning w-100" title="Desativar">
                                    <i class="fas fa-pause"></i>
                                </button>
                            </form>
                        {% else %}
                            <form method="post" action="{% url 'config:user_activate' user.slug %}" class="d-inline w-100" onsubmit="return confirm('Tem certeza que deseja ativar este usuário?')">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-outline-success w-100" title="Ativar">
                                    <i class="fas fa-play"></i>
                                </button>
                            </form>
                        {% endif %}
                        <a href="{% url 'config:user_delete' user.slug %}" class="btn btn-outline-danger w-100" title="Deletar">
                            <i class="fas fa-trash"></i>
                        </a>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="text-center text-muted">Nenhum usuário encontrado.</div>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}
# Generated by Django 5.2.2 on 2025-06-06 14:51

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SEOSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('site_name', models.CharField(help_text='Nome do site para SEO', max_length=100, verbose_name='nome do site')),
                ('site_description', models.TextField(help_text='Descrição geral do site (máximo 160 caracteres)', max_length=160, validators=[django.core.validators.MaxLengthValidator(160)], verbose_name='descrição do site')),
                ('site_keywords', models.TextField(blank=True, help_text='Palavras-chave gerais separadas por vírgula', max_length=255, verbose_name='palavras-chave do site')),
                ('default_og_image', models.ImageField(blank=True, help_text='Imagem padrão para compartilhamento em redes sociais', upload_to='seo/og_images/', verbose_name='imagem padrão Open Graph')),
                ('favicon', models.ImageField(blank=True, help_text='Ícone do site (favicon)', upload_to='seo/favicons/', verbose_name='favicon')),
                ('google_analytics_id', models.CharField(blank=True, help_text='ID do Google Analytics (ex: GA_MEASUREMENT_ID)', max_length=20, verbose_name='Google Analytics ID')),
                ('google_tag_manager_id', models.CharField(blank=True, help_text='ID do Google Tag Manager (ex: GTM-XXXXXXX)', max_length=20, verbose_name='Google Tag Manager ID')),
                ('facebook_pixel_id', models.CharField(blank=True, help_text='ID do Facebook Pixel', max_length=20, verbose_name='Facebook Pixel ID')),
                ('facebook_url', models.URLField(blank=True, help_text='URL da página do Facebook', verbose_name='URL do Facebook')),
                ('twitter_url', models.URLField(blank=True, help_text='URL do perfil do Twitter', verbose_name='URL do Twitter')),
                ('instagram_url', models.URLField(blank=True, help_text='URL do perfil do Instagram', verbose_name='URL do Instagram')),
                ('linkedin_url', models.URLField(blank=True, help_text='URL do perfil do LinkedIn', verbose_name='URL do LinkedIn')),
                ('youtube_url', models.URLField(blank=True, help_text='URL do canal do YouTube', verbose_name='URL do YouTube')),
                ('contact_email', models.EmailField(blank=True, help_text='Email principal de contato', max_length=254, verbose_name='email de contato')),
                ('contact_phone', models.CharField(blank=True, help_text='Telefone principal de contato', max_length=20, verbose_name='telefone de contato')),
                ('address', models.TextField(blank=True, help_text='Endereço físico da empresa', verbose_name='endereço')),
                ('organization_type', models.CharField(default='Organization', help_text='Tipo de organização para Schema.org', max_length=50, verbose_name='tipo de organização')),
                ('robots_txt', models.TextField(blank=True, help_text='Conteúdo do arquivo robots.txt', verbose_name='robots.txt')),
                ('enable_sitemap', models.BooleanField(default=True, help_text='Se deve gerar sitemap automaticamente', verbose_name='habilitar sitemap')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='criado em')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='atualizado em')),
            ],
            options={
                'verbose_name': 'configuração de SEO',
                'verbose_name_plural': 'configurações de SEO',
            },
        ),
        migrations.CreateModel(
            name='Page',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Título da página', max_length=200, validators=[django.core.validators.MinLengthValidator(3)], verbose_name='título')),
                ('slug', models.SlugField(help_text='URL amigável da página', max_length=200, unique=True, verbose_name='slug')),
                ('content', models.TextField(help_text='Conteúdo da página em HTML ou Markdown', verbose_name='conteúdo')),
                ('excerpt', models.TextField(blank=True, help_text='Resumo da página para SEO e listagens', max_length=500, verbose_name='resumo')),
                ('status', models.CharField(choices=[('draft', 'Rascunho'), ('published', 'Publicado'), ('archived', 'Arquivado')], default='draft', help_text='Status de publicação da página', max_length=20, verbose_name='status')),
                ('template', models.CharField(choices=[('pages/default.html', 'Padrão'), ('pages/landing.html', 'Landing Page'), ('pages/full_width.html', 'Largura Total'), ('pages/sidebar.html', 'Com Sidebar')], default='pages/default.html', help_text='Template a ser usado para renderizar a página', max_length=100, verbose_name='template')),
                ('is_homepage', models.BooleanField(default=False, help_text='Define se esta é a página inicial do site', verbose_name='é página inicial')),
                ('show_in_menu', models.BooleanField(default=True, help_text='Se a página deve aparecer no menu de navegação', verbose_name='mostrar no menu')),
                ('menu_order', models.PositiveIntegerField(default=0, help_text='Ordem de exibição no menu (menor número = primeiro)', verbose_name='ordem no menu')),
                ('meta_title', models.CharField(blank=True, help_text='Título para SEO (máximo 60 caracteres)', max_length=60, verbose_name='meta título')),
                ('meta_description', models.CharField(blank=True, help_text='Descrição para SEO (máximo 160 caracteres)', max_length=160, verbose_name='meta descrição')),
                ('meta_keywords', models.CharField(blank=True, help_text='Palavras-chave separadas por vírgula', max_length=255, verbose_name='palavras-chave')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='criado em')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='atualizado em')),
                ('published_at', models.DateTimeField(blank=True, help_text='Data e hora de publicação', null=True, verbose_name='publicado em')),
                ('view_count', models.PositiveIntegerField(default=0, help_text='Número de visualizações da página', verbose_name='visualizações')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_pages', to=settings.AUTH_USER_MODEL, verbose_name='criado por')),
                ('parent', models.ForeignKey(blank=True, help_text='Página pai para criar hierarquia', null=True, on_delete=django.db.models.deletion.CASCADE, to='pages.page', verbose_name='página pai')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_pages', to=settings.AUTH_USER_MODEL, verbose_name='atualizado por')),
            ],
            options={
                'verbose_name': 'página',
                'verbose_name_plural': 'páginas',
                'ordering': ['menu_order', 'title'],
            },
        ),
        migrations.CreateModel(
            name='NavigationItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Texto exibido no menu', max_length=100, verbose_name='título')),
                ('url', models.CharField(blank=True, help_text='URL personalizada (para links externos)', max_length=255, verbose_name='URL')),
                ('nav_type', models.CharField(choices=[('page', 'Página'), ('url', 'URL Externa'), ('category', 'Categoria')], default='page', help_text='Tipo de item de navegação', max_length=20, verbose_name='tipo')),
                ('order', models.PositiveIntegerField(default=0, help_text='Ordem de exibição no menu', verbose_name='ordem')),
                ('is_active', models.BooleanField(default=True, help_text='Se o item deve ser exibido no menu', verbose_name='ativo')),
                ('open_in_new_tab', models.BooleanField(default=False, help_text='Se o link deve abrir em nova aba', verbose_name='abrir em nova aba')),
                ('css_class', models.CharField(blank=True, help_text='Classes CSS personalizadas para o item', max_length=100, verbose_name='classe CSS')),
                ('icon', models.CharField(blank=True, help_text='Classe do ícone (ex: fas fa-home)', max_length=50, verbose_name='ícone')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='criado em')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='atualizado em')),
                ('parent', models.ForeignKey(blank=True, help_text='Item pai para criar submenus', null=True, on_delete=django.db.models.deletion.CASCADE, to='pages.navigationitem', verbose_name='item pai')),
                ('page', models.ForeignKey(blank=True, help_text='Página interna do site', null=True, on_delete=django.db.models.deletion.CASCADE, to='pages.page', verbose_name='página')),
            ],
            options={
                'verbose_name': 'item de navegação',
                'verbose_name_plural': 'itens de navegação',
                'ordering': ['order', 'title'],
            },
        ),
        migrations.AddIndex(
            model_name='page',
            index=models.Index(fields=['slug'], name='pages_page_slug_3e99a9_idx'),
        ),
        migrations.AddIndex(
            model_name='page',
            index=models.Index(fields=['status', 'published_at'], name='pages_page_status_de97e2_idx'),
        ),
        migrations.AddIndex(
            model_name='page',
            index=models.Index(fields=['show_in_menu', 'menu_order'], name='pages_page_show_in_ad7186_idx'),
        ),
    ]

/* TinyMCE Content CSS - Project Nix */

/* === TEMA CLARO (PADRÃO) === */
body {
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    line-height: 1.6;
    color: #0f172a;
    background-color: #ffffff;
    margin: 1rem;
    font-size: 14px;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Roboto', sans-serif;
    font-weight: 500;
    line-height: 1.3;
    color: #0f172a;
    margin-bottom: 0.75rem;
    margin-top: 1rem;
}

h1 { font-size: 2rem; }
h2 { font-size: 1.75rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: 1rem;
    color: #0f172a;
}

a {
    color: #7c3aed;
    text-decoration: none;
}

a:hover {
    color: #5b21b6;
    text-decoration: underline;
}

blockquote {
    border-left: 4px solid #7c3aed;
    background-color: #f8fafc;
    padding: 1rem;
    margin: 1rem 0;
    font-style: italic;
    color: #475569;
}

code {
    background-color: #f1f5f9;
    color: #7c3aed;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
    font-size: 0.875em;
    border: 1px solid #e2e8f0;
}

pre {
    background-color: #f1f5f9;
    color: #0f172a;
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    border: 1px solid #e2e8f0;
    font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
}

pre code {
    background: none;
    border: none;
    padding: 0;
    color: inherit;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
    border: 1px solid #e2e8f0;
}

th, td {
    border: 1px solid #e2e8f0;
    padding: 0.75rem;
    text-align: left;
}

th {
    background-color: #f8fafc;
    font-weight: 600;
    color: #0f172a;
}

ul, ol {
    margin-bottom: 1rem;
    padding-left: 2rem;
}

li {
    margin-bottom: 0.5rem;
    color: #0f172a;
}

img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    margin: 1rem 0;
}

hr {
    border: none;
    border-top: 2px solid #e2e8f0;
    margin: 2rem 0;
}

/* === TEMA ESCURO === */
[data-theme="dark"] body {
    color: #ffffff !important;
    background-color: #0f172a !important;
}

[data-theme="dark"] h1,
[data-theme="dark"] h2,
[data-theme="dark"] h3,
[data-theme="dark"] h4,
[data-theme="dark"] h5,
[data-theme="dark"] h6 {
    color: #ffffff !important;
}

[data-theme="dark"] p {
    color: #ffffff !important;
}

[data-theme="dark"] a {
    color: #a855f7 !important;
}

[data-theme="dark"] a:hover {
    color: #c084fc !important;
}

[data-theme="dark"] blockquote {
    border-left-color: #7c3aed !important;
    background-color: #1e293b !important;
    color: #e2e8f0 !important;
}

[data-theme="dark"] code {
    background-color: #1e293b !important;
    color: #a855f7 !important;
    border-color: #475569 !important;
}

[data-theme="dark"] pre {
    background-color: #1e293b !important;
    color: #ffffff !important;
    border-color: #475569 !important;
}

[data-theme="dark"] table {
    border-color: #475569 !important;
}

[data-theme="dark"] th,
[data-theme="dark"] td {
    border-color: #475569 !important;
    color: #ffffff !important;
}

[data-theme="dark"] th {
    background-color: #1e293b !important;
}

[data-theme="dark"] li {
    color: #ffffff !important;
}

[data-theme="dark"] hr {
    border-top-color: #475569 !important;
}

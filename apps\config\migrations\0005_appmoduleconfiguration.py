# Generated by Django 5.2.2 on 2025-06-10 17:53

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('config', '0004_alter_emailconfiguration_created_by_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AppModuleConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('app_name', models.CharField(help_text='Nome do app Django (ex: articles, blog, shop)', max_length=100, unique=True, validators=[django.core.validators.MinLengthValidator(3)], verbose_name='nome do app')),
                ('display_name', models.CharField(help_text='Nome amigável para exibição na interface', max_length=200, verbose_name='nome de exibição')),
                ('description', models.TextField(blank=True, help_text='Descrição detalhada do módulo', verbose_name='descrição')),
                ('module_type', models.CharField(choices=[('core', 'Módulo Principal'), ('feature', 'Funcionalidade'), ('integration', 'Integração'), ('custom', 'Personalizado')], default='feature', help_text='Tipo/categoria do módulo', max_length=20, verbose_name='tipo do módulo')),
                ('status', models.CharField(choices=[('active', 'Ativo'), ('inactive', 'Inativo'), ('maintenance', 'Manutenção'), ('deprecated', 'Descontinuado')], default='active', help_text='Status atual do módulo', max_length=20, verbose_name='status')),
                ('is_enabled', models.BooleanField(default=True, help_text='Se o módulo está habilitado no sistema', verbose_name='habilitado')),
                ('is_core', models.BooleanField(default=False, help_text='Se é um módulo principal (não pode ser desabilitado)', verbose_name='módulo principal')),
                ('url_pattern', models.CharField(blank=True, help_text='Padrão de URL do módulo (ex: artigos/, blog/)', max_length=200, verbose_name='padrão de URL')),
                ('menu_icon', models.CharField(blank=True, default='fas fa-puzzle-piece', help_text='Classe do ícone FontAwesome para o menu', max_length=100, verbose_name='ícone do menu')),
                ('menu_order', models.PositiveIntegerField(default=100, help_text='Ordem de exibição no menu (menor = primeiro)', verbose_name='ordem no menu')),
                ('show_in_menu', models.BooleanField(default=True, help_text='Se deve aparecer no menu de navegação', verbose_name='exibir no menu')),
                ('dependencies', models.JSONField(blank=True, default=list, help_text='Lista de apps que este módulo depende', verbose_name='dependências')),
                ('required_permissions', models.JSONField(blank=True, default=list, help_text='Lista de permissões necessárias para usar o módulo', verbose_name='permissões necessárias')),
                ('module_settings', models.JSONField(blank=True, default=dict, help_text='Configurações específicas do módulo em formato JSON', verbose_name='configurações do módulo')),
                ('version', models.CharField(blank=True, help_text='Versão do módulo', max_length=20, verbose_name='versão')),
                ('author', models.CharField(blank=True, help_text='Autor/desenvolvedor do módulo', max_length=200, verbose_name='autor')),
                ('documentation_url', models.URLField(blank=True, help_text='Link para documentação do módulo', verbose_name='URL da documentação')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='criado em')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='atualizado em')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='modules_created', to=settings.AUTH_USER_MODEL, verbose_name='criado por')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='modules_updated', to=settings.AUTH_USER_MODEL, verbose_name='atualizado por')),
            ],
            options={
                'verbose_name': 'configuração de módulo',
                'verbose_name_plural': 'configurações de módulos',
                'ordering': ['menu_order', 'display_name'],
                'indexes': [models.Index(fields=['app_name'], name='config_appm_app_nam_abae83_idx'), models.Index(fields=['is_enabled', 'status'], name='config_appm_is_enab_c54106_idx'), models.Index(fields=['module_type'], name='config_appm_module__6ee927_idx')],
            },
        ),
    ]

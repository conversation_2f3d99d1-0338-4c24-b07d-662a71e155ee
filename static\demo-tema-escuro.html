<!DOCTYPE html>
<html lang="pt-br" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demonstração - Tema Escuro Corrigido</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <link href="css/main.css" rel="stylesheet">
    <link href="css/accessibility.css" rel="stylesheet">
    <link href="css/tinymce-content.css" rel="stylesheet">
    <style>
        .demo-section {
            padding: 2rem 0;
            border-bottom: 1px solid var(--border-color);
        }
        .color-demo {
            padding: 1rem;
            border-radius: var(--border-radius);
            margin: 0.5rem 0;
            border: 1px solid var(--border-color);
        }
        .theme-toggle-demo {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        .text-sample {
            padding: 1rem;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            margin: 1rem 0;
            background-color: var(--bg-secondary);
        }
        .editor-demo {
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 1rem;
            background-color: var(--bg-color);
            color: var(--text-color);
            min-height: 200px;
            font-family: var(--font-family-sans-serif);
        }
    </style>
</head>
<body>
    <div class="theme-toggle-demo">
        <button class="btn btn-outline-secondary" onclick="toggleTheme()">
            <i class="fas fa-sun" id="theme-icon"></i>
        </button>
    </div>

    <!-- Navbar de Teste -->
    <nav class="navbar navbar-expand-lg navbar-nix">
        <div class="container">
            <!-- Brand -->
            <a class="navbar-brand" href="#">
                <img src="favicon.ico" alt="Project Nix Logo" width="32" height="32">
                Project Nix
            </a>
            
            <!-- Mobile Toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <!-- Navigation Links -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#">
                            <i class="fas fa-home"></i>Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-newspaper"></i>Artigos
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog"></i>Configurações
                        </a>
                        <ul class="dropdown-menu">
                            <li><h6 class="dropdown-header">Menu de Teste</h6></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Perfil</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Configurações</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="#"><i class="fas fa-sign-out-alt me-2"></i>Sair</a></li>
                        </ul>
                    </li>
                </ul>
                
                <!-- Search Form -->
                <form class="d-flex me-3 form-nix align-items-center" method="get" action="#" aria-label="Formulário de busca" role="form">
                    <div class="input-group">
                        <input class="form-control form-control-enhanced" type="search" name="q" placeholder="Buscar..."
                               aria-label="Campo de busca" aria-describedby="search-button" value="">
                        <button class="btn btn-outline-light text-sans" type="submit"
                                id="search-button" aria-label="Buscar">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
                
                <!-- User Menu -->
                <ul class="navbar-nav align-items-center">
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-user"></i>Login
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container py-5">
        <div class="text-center mb-5">
            <h1 class="display-4 fw-bold" style="color: var(--nix-accent);">Tema Escuro Corrigido</h1>
            <p class="lead">Textos brancos e TinyMCE otimizado</p>
        </div>

        <!-- Correções Implementadas -->
        <section class="demo-section">
            <h2 class="h3 mb-4">✅ Correções Implementadas</h2>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-palette text-primary me-2"></i>
                                Cores de Texto
                            </h5>
                            <p class="card-text">Textos agora são brancos puros no tema escuro.</p>
                            <div class="color-demo" style="background-color: var(--text-color); color: var(--bg-color);">
                                Texto Principal: #ffffff
                            </div>
                            <div class="color-demo" style="background-color: var(--text-muted); color: var(--bg-color);">
                                Texto Secundário: #e2e8f0
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-edit text-primary me-2"></i>
                                TinyMCE
                            </h5>
                            <p class="card-text">Editor completamente otimizado para tema escuro.</p>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Toolbar escura</li>
                                <li><i class="fas fa-check text-success me-2"></i>Conteúdo branco</li>
                                <li><i class="fas fa-check text-success me-2"></i>Menus escuros</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Teste de Textos -->
        <section class="demo-section">
            <h2 class="h3 mb-4">📝 Teste de Textos</h2>
            
            <div class="text-sample">
                <h3>Título Principal</h3>
                <p>Este é um parágrafo de texto normal que deve aparecer em branco puro no tema escuro.</p>
                <p class="text-muted">Este é um texto secundário que deve aparecer em cinza claro.</p>
                <p class="text-light">Este é um texto light que deve aparecer em cinza médio.</p>
                <a href="#" class="me-3">Link Normal</a>
                <a href="#" class="text-muted">Link Muted</a>
            </div>
        </section>

        <!-- Teste de Formulários -->
        <section class="demo-section">
            <h2 class="h3 mb-4">📋 Teste de Formulários</h2>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Campo de Texto</label>
                        <input type="text" class="form-control" placeholder="Digite algo...">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Select</label>
                        <select class="form-select">
                            <option>Opção 1</option>
                            <option>Opção 2</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Textarea</label>
                        <textarea class="form-control" rows="4" placeholder="Digite uma mensagem..."></textarea>
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <button type="button" class="btn btn-primary me-2">Botão Primário</button>
                <button type="button" class="btn btn-outline-secondary me-2">Botão Outline</button>
                <button type="button" class="btn btn-secondary">Botão Secundário</button>
            </div>
        </section>

        <!-- Simulação do TinyMCE -->
        <section class="demo-section">
            <h2 class="h3 mb-4">✏️ Simulação do Editor TinyMCE</h2>
            
            <div class="editor-demo">
                <h3>Título do Artigo</h3>
                <p>Este é um exemplo de como o conteúdo aparece no editor TinyMCE com o tema escuro.</p>
                
                <blockquote>
                    Esta é uma citação que deve aparecer com fundo escuro e borda roxa.
                </blockquote>
                
                <p>Aqui temos um <code>código inline</code> que deve aparecer destacado.</p>
                
                <pre><code>// Exemplo de código
function exemploTemaEscuro() {
    return "Texto branco em fundo escuro";
}</code></pre>
                
                <p>E aqui temos uma <a href="#">link roxo</a> que deve ser visível.</p>
                
                <table style="width: 100%; border-collapse: collapse; margin: 1rem 0;">
                    <thead>
                        <tr>
                            <th style="border: 1px solid var(--border-color); padding: 0.5rem; background-color: var(--bg-secondary);">Coluna 1</th>
                            <th style="border: 1px solid var(--border-color); padding: 0.5rem; background-color: var(--bg-secondary);">Coluna 2</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td style="border: 1px solid var(--border-color); padding: 0.5rem;">Dados 1</td>
                            <td style="border: 1px solid var(--border-color); padding: 0.5rem;">Dados 2</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <!-- Teste de Alertas -->
        <section class="demo-section">
            <h2 class="h3 mb-4">🚨 Teste de Alertas</h2>
            
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle me-2"></i>Alerta de Informação</h5>
                <p class="mb-0">Este é um alerta de informação com texto branco.</p>
            </div>
            
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle me-2"></i>Alerta de Sucesso</h5>
                <p class="mb-0">Este é um alerta de sucesso com texto branco.</p>
            </div>
            
            <div class="alert alert-warning">
                <h5><i class="fas fa-exclamation-triangle me-2"></i>Alerta de Aviso</h5>
                <p class="mb-0">Este é um alerta de aviso com texto branco.</p>
            </div>
            
            <div class="alert alert-danger">
                <h5><i class="fas fa-times-circle me-2"></i>Alerta de Erro</h5>
                <p class="mb-0">Este é um alerta de erro com texto branco.</p>
            </div>
        </section>

        <!-- Instruções -->
        <section class="demo-section">
            <h2 class="h3 mb-4">🧪 Como Testar</h2>
            
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle me-2"></i>Instruções de Teste</h5>
                <ol>
                    <li><strong>Use o toggle de tema</strong> no canto superior direito</li>
                    <li><strong>Observe os textos</strong> - devem ser brancos puros no tema escuro</li>
                    <li><strong>Teste os formulários</strong> - clique nos campos para ver o focus</li>
                    <li><strong>Verifique os dropdowns</strong> - abra o menu de configurações</li>
                    <li><strong>Compare com o tema claro</strong> para ver a diferença</li>
                </ol>
            </div>
        </section>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function toggleTheme() {
            const html = document.documentElement;
            const icon = document.getElementById('theme-icon');
            const currentTheme = html.getAttribute('data-theme');
            
            if (currentTheme === 'dark') {
                html.setAttribute('data-theme', 'light');
                icon.className = 'fas fa-moon';
            } else {
                html.setAttribute('data-theme', 'dark');
                icon.className = 'fas fa-sun';
            }
        }

        // Aplicar tema escuro inicial
        document.documentElement.setAttribute('data-theme', 'dark');

        // Log das cores para debug
        document.addEventListener('DOMContentLoaded', function() {
            const computedStyle = getComputedStyle(document.documentElement);
            console.log('🎨 Cores do tema escuro:');
            console.log('--text-color:', computedStyle.getPropertyValue('--text-color'));
            console.log('--text-muted:', computedStyle.getPropertyValue('--text-muted'));
            console.log('--text-light:', computedStyle.getPropertyValue('--text-light'));
            console.log('--bg-color:', computedStyle.getPropertyValue('--bg-color'));
        });
    </script>
</body>
</html>

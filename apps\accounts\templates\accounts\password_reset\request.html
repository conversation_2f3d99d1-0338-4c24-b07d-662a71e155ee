{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}Redefinir <PERSON> - {{ block.super }}{% endblock %}

{% block content %}
<div class="container my-5">
    <!-- Header da <PERSON> -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-1 text-sans text-body">
                        <i class="fas fa-key me-2 text-django-green"></i>Redefinir Senha
                    </h1>
                    <p class="text-secondary mb-0 text-body">Recupere o acesso à sua conta</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <!-- Card de Reset de Senha -->
            <div class="card-django border-0 shadow-sm">
                <div class="profile-card-header bg-warning text-dark text-center">
                    <h4 class="mb-0 text-sans text-body">
                        <i class="fas fa-key me-2"></i>Recuperar <PERSON>
                    </h4>
                </div>
                <div class="card-body profile-card-body">
                    <div class="text-center mb-4">
                        <i class="fas fa-envelope fa-3x text-warning mb-3"></i>
                        <p class="text-secondary text-body">
                            Digite seu e-mail para receber um código de redefinição de senha.
                        </p>
                    </div>

                    <!-- Mensagens -->
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {% if message.tags == 'success' %}
                                    <i class="fas fa-check-circle me-2"></i>
                                {% elif message.tags == 'error' %}
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                {% elif message.tags == 'warning' %}
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                {% else %}
                                    <i class="fas fa-info-circle me-2"></i>
                                {% endif %}
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <!-- Formulário de Reset -->
                    <form method="post" class="needs-validation form-django" novalidate>
                        {% csrf_token %}
                        {{ form|crispy }}

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-warning btn-lg text-sans">
                                <i class="fas fa-paper-plane me-2"></i>Enviar Código
                            </button>
                        </div>
                    </form>

                    <!-- Dica -->
                    <div class="mt-4">
                        <div class="alert alert-info border-0">
                            <div class="d-flex align-items-start">
                                <i class="fas fa-info-circle text-info me-2 mt-1"></i>
                                <div class="small">
                                    <strong>Dica:</strong> Verifique sua caixa de entrada e também a pasta de spam.
                                    O código tem validade de 15 minutos.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Links Úteis -->
            <div class="text-center mt-4">
                <p class="text-secondary">
                    Lembrou da senha?
                    <a href="{% url 'accounts:login' %}" class="text-django-green text-decoration-none">
                        <i class="fas fa-sign-in-alt me-1"></i>Fazer Login
                    </a>
                </p>
                <p class="text-secondary">
                    Não tem uma conta?
                    <a href="{% url 'accounts:register' %}" class="text-django-green text-decoration-none">
                        <i class="fas fa-user-plus me-1"></i>Criar Conta
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus no campo de email
    const emailField = document.getElementById('id_email');
    if (emailField) {
        emailField.focus();
    }

    // Validação de formulário
    const form = document.querySelector('.needs-validation');
    if (form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            } else {
                // Feedback visual no envio
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Enviando...';
                    submitBtn.disabled = true;
                }
            }
            form.classList.add('was-validated');
        });
    }

    // Validação de email em tempo real
    if (emailField) {
        emailField.addEventListener('input', function() {
            const email = this.value;
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

            this.classList.remove('is-valid', 'is-invalid');

            if (email && emailRegex.test(email)) {
                this.classList.add('is-valid');
            } else if (email) {
                this.classList.add('is-invalid');
            }
        });
    }

    // Auto-dismiss de alertas após 5 segundos
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert.parentElement) {
                alert.classList.remove('show');
                setTimeout(() => alert.remove(), 150);
            }
        }, 5000);
    });
});
</script>
{% endblock %}

{# Tabela acessível e DRY para listagens. Fallback seguro para headers/rows vazios. #}
<table class="table table-hover table-striped align-middle mb-0" role="table" aria-describedby="{{ caption|default:'Tabela de dados'|slugify }}-caption">
    {% if caption %}
        <caption id="{{ caption|slugify }}-caption" class="visually-hidden">{{ caption }}</caption>
    {% endif %}
    <thead class="table-light">
        <tr>
            {% if headers %}
                {% for header in headers %}
                    <th scope="col">{{ header.label }}</th>
                {% endfor %}
            {% else %}
                <th scope="col">Coluna 1</th>
            {% endif %}
        </tr>
    </thead>
    <tbody>
        <tr><td colspan="99" style="color:red;">DEBUG: Renderizando linhas da tabela ({{ rows|length }})</td></tr>
        {% if rows %}
            {% for row in rows %}
                <tr tabindex="0">
                    {% for cell in row %}
                        <td>{{ cell|safe }}</td>
                    {% endfor %}
                </tr>
            {% empty %}
                <tr>
                    <td colspan="{{ headers|length|default:1 }}" class="text-center text-secondary">Nenhum dado encontrado.</td>
                </tr>
            {% endfor %}
        {% else %}
            <tr>
                <td colspan="{{ headers|length|default:1 }}" class="text-center text-secondary">Nenhum dado encontrado.</td>
            </tr>
        {% endif %}
    </tbody>
</table> 
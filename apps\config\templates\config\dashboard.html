{% extends 'config/base_config_page.html' %}
{% load config_extras %}

{% block config_title %}Dashboard{% endblock %}
{% block page_icon %}<i class="fas fa-tachometer-alt me-2"></i>{% endblock %}
{% block page_title %}Dashboard{% endblock %}

{% block page_content %}
<!-- Cards de Estatísticas -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow-sm h-100 py-2 config-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            <i class="fas fa-users me-1"></i>Usuários
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_users }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow-sm h-100 py-2 config-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            <i class="fas fa-cubes me-1"></i>Módulos Ativos
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.active_modules }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-cubes fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow-sm h-100 py-2 config-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            <i class="fas fa-envelope me-1"></i>Emails Enviados
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.emails_sent|default:"0" }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-envelope fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow-sm h-100 py-2 config-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            <i class="fas fa-layer-group me-1"></i>Grupos
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_groups }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-layer-group fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Conteúdo Principal -->
<div class="row">
    <!-- Status do Sistema -->
    <div class="col-lg-8 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    <i class="fas fa-server me-2"></i>Status do Sistema
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm me-3">
                                <div class="bg-success rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="fas fa-check text-white"></i>
                                </div>
                            </div>
                            <div>
                                <div class="fw-bold">Sistema Operacional</div>
                                <small class="text-muted">{{ system_info.os|default:"Desconhecido" }}</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm me-3">
                                <div class="bg-info rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="fas fa-code text-white"></i>
                                </div>
                            </div>
                            <div>
                                <div class="fw-bold">Versão Python</div>
                                <small class="text-muted">{{ system_info.python_version|default:"Desconhecida" }}</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm me-3">
                                <div class="bg-warning rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="fas fa-database text-white"></i>
                                </div>
                            </div>
                            <div>
                                <div class="fw-bold">Banco de Dados</div>
                                <small class="text-muted">{{ system_info.database|default:"Desconhecido" }}</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm me-3">
                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="fas fa-envelope text-white"></i>
                                </div>
                            </div>
                            <div>
                                <div class="fw-bold">Email</div>
                                <small class="text-muted">
                                    {% if email_config %}
                                        <span class="text-success">Configurado</span>
                                    {% else %}
                                        <span class="text-danger">Não configurado</span>
                                    {% endif %}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Ações Rápidas -->
    <div class="col-lg-4 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>Ações Rápidas
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'config:user_list' %}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-users me-2"></i>Gerenciar Usuários
                    </a>
                    <a href="{% url 'config:module_list' %}" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-cubes me-2"></i>Gerenciar Módulos
                    </a>
                    <a href="{% url 'config:email_config' %}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-envelope me-2"></i>Configurar Email
                    </a>
                    <a href="{% url 'config:group_list' %}" class="btn btn-outline-warning btn-sm">
                        <i class="fas fa-layer-group me-2"></i>Gerenciar Grupos
                    </a>
                    <a href="{% url 'config:backup_database' %}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-database me-2"></i>Backup do Banco de Dados
                    </a>
                    <a href="{% url 'config:restore_database' %}" class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-database me-2"></i>Restaurar Banco de Dados
                    </a>
                    <a href="{% url 'config:backup_media' %}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-folder-open me-2"></i>Backup da Mídia
                    </a>
                    <a href="{% url 'config:restore_media' %}" class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-folder-open me-2"></i>Restaurar Mídia
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Módulos Recentes -->
<div class="row">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cubes me-2"></i>Módulos do Sistema
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive d-none d-md-block">
                    <table class="table table-hover align-middle mb-0">
                        <thead class="table-light">
                            <tr>
                                <th scope="col"><i class="fas fa-cube"></i> Módulo</th>
                                <th scope="col"><i class="fas fa-align-left"></i> Descrição</th>
                                <th scope="col"><i class="fas fa-toggle-on"></i> Status</th>
                                <th scope="col"><i class="fas fa-cogs"></i> Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                        {% for module in recent_modules %}
                            <tr{% if module.is_core %} class="table-success"{% endif %}>
                                <td>
                                    <span class="fw-bold">{{ module.display_name }}</span>
                                    {% if module.is_core %}
                                        <span class="badge bg-success ms-2" title="Módulo essencial"><i class="fas fa-star"></i> Core</span>
                                    {% endif %}
                                </td>
                                <td>{{ module.description|default:'—' }}</td>
                                <td>
                                    {% if module.is_enabled %}
                                        <span class="badge bg-success"><i class="fas fa-check-circle"></i> Ativo</span>
                                    {% else %}
                                        <span class="badge bg-secondary"><i class="fas fa-times-circle"></i> Inativo</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'config:module_detail' module.app_name %}" class="btn btn-outline-info btn-sm" title="Detalhes">
                                        <i class="fas fa-info-circle"></i>
                                    </a>
                                </td>
                            </tr>
                        {% empty %}
                            <tr><td colspan="4" class="text-center text-muted">Nenhum módulo encontrado.</td></tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Layout responsivo para mobile -->
                <div class="d-block d-md-none">
                    {% for module in recent_modules %}
                    <div class="card mb-2 {% if module.is_core %}border-success{% endif %}">
                        <div class="card-body py-2 px-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span class="fw-bold">{{ module.display_name }}</span>
                                {% if module.is_core %}
                                    <span class="badge bg-success ms-2" title="Módulo essencial"><i class="fas fa-star"></i> Core</span>
                                {% endif %}
                            </div>
                            <div class="mb-1 small text-muted">{{ module.description|default:'—' }}</div>
                            <div class="mb-2">
                                {% if module.is_enabled %}
                                    <span class="badge bg-success"><i class="fas fa-check-circle"></i> Ativo</span>
                                {% else %}
                                    <span class="badge bg-secondary"><i class="fas fa-times-circle"></i> Inativo</span>
                                {% endif %}
                            </div>
                            <div class="text-center">
                                <a href="{% url 'config:module_detail' module.app_name %}" class="btn btn-outline-info btn-sm" title="Detalhes">
                                    <i class="fas fa-info-circle"></i> Ver Detalhes
                                </a>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center text-muted p-3">Nenhum módulo encontrado.</div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
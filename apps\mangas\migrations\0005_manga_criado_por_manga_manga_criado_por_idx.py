# Generated by Django 5.2.4 on 2025-07-28 16:00

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mangas', '0004_volume_slug'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='manga',
            name='criado_por',
            field=models.ForeignKey(blank=True, help_text='Usuário que criou este mangá', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='Criado por'),
        ),
        migrations.AddIndex(
            model_name='manga',
            index=models.Index(fields=['criado_por'], name='manga_criado_por_idx'),
        ),
    ]

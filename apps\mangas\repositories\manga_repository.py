from django.shortcuts import get_object_or_404
from django.db.models import QuerySet
from typing import Dict, Any

from ..interfaces.manga_repository_interface import MangaRepositoryInterface
from ..models.manga import Manga
from ..models.capitulo import Capitulo
from ..models.pagina import Pagina
from ..exceptions import MangaNotFoundError, ChapterNotFoundError, PageNotFoundError

class MangaRepository(MangaRepositoryInterface):
    """
    Implementação concreta do repositório de mangás.
    
    Esta classe implementa a interface MangaRepositoryInterface,
    fornecendo acesso aos dados seguindo os princípios SOLID.
    """
    
    def list_mangas(self) -> QuerySet[Manga]:
        """Lista todos os mangás."""
        return Manga.objects.all()
    
    def get_manga_by_slug(self, slug: str) -> Manga:
        """Obtém um mangá pelo slug."""
        try:
            return get_object_or_404(Manga, slug=slug)
        except Exception as e:
            raise MangaNotFoundError(f"Mangá com slug '{slug}' não encontrado: {str(e)}")
    
    def create_manga(self, data: Dict[str, Any]) -> Manga:
        """Cria um novo mangá."""
        try:
            return Manga.objects.create(**data)
        except Exception as e:
            raise Exception(f"Erro ao criar mangá: {str(e)}")
    
    def update_manga(self, slug: str, data: Dict[str, Any]) -> Manga:
        """Atualiza um mangá existente."""
        try:
            manga = self.get_manga_by_slug(slug)
            for key, value in data.items():
                setattr(manga, key, value)
            manga.save()
            return manga
        except Exception as e:
            raise Exception(f"Erro ao atualizar mangá: {str(e)}")
    
    def delete_manga(self, slug: str) -> None:
        """Exclui um mangá."""
        try:
            manga = self.get_manga_by_slug(slug)
            manga.delete()
        except Exception as e:
            raise Exception(f"Erro ao excluir mangá: {str(e)}")
    
    def list_capitulos(self, manga: Manga) -> QuerySet[Capitulo]:
        """Lista todos os capítulos de um mangá."""
        return Capitulo.objects.filter(volume__manga=manga)
    
    def get_capitulo_by_slug(self, manga: Manga, capitulo_slug: str) -> Capitulo:
        """Obtém um capítulo pelo slug."""
        try:
            return get_object_or_404(Capitulo, volume__manga=manga, slug=capitulo_slug)
        except Exception as e:
            raise ChapterNotFoundError(f"Capítulo com slug '{capitulo_slug}' não encontrado: {str(e)}")
    
    def create_capitulo(self, manga: Manga, data: Dict[str, Any]) -> Capitulo:
        """Cria um novo capítulo."""
        try:
            return Capitulo.objects.create(**data)
        except Exception as e:
            raise Exception(f"Erro ao criar capítulo: {str(e)}")
    
    def update_capitulo(self, manga: Manga, capitulo_slug: str, data: Dict[str, Any]) -> Capitulo:
        """Atualiza um capítulo existente."""
        try:
            capitulo = self.get_capitulo_by_slug(manga, capitulo_slug)
            for key, value in data.items():
                setattr(capitulo, key, value)
            capitulo.save()
            return capitulo
        except Exception as e:
            raise Exception(f"Erro ao atualizar capítulo: {str(e)}")
    
    def delete_capitulo(self, manga: Manga, capitulo_slug: str) -> None:
        """Exclui um capítulo."""
        try:
            capitulo = self.get_capitulo_by_slug(manga, capitulo_slug)
            capitulo.delete()
        except Exception as e:
            raise Exception(f"Erro ao excluir capítulo: {str(e)}")
    
    def list_paginas(self, capitulo: Capitulo) -> QuerySet[Pagina]:
        """Lista todas as páginas de um capítulo."""
        return capitulo.paginas.all()
    
    def get_pagina(self, capitulo: Capitulo, number: int) -> Pagina:
        """Obtém uma página pelo número."""
        try:
            return get_object_or_404(Pagina, capitulo=capitulo, number=number)
        except Exception as e:
            raise PageNotFoundError(f"Página {number} não encontrada: {str(e)}")
    
    def create_pagina(self, capitulo: Capitulo, data: Dict[str, Any]) -> Pagina:
        """Cria uma nova página."""
        try:
            return Pagina.objects.create(capitulo=capitulo, **data)
        except Exception as e:
            raise Exception(f"Erro ao criar página: {str(e)}")
    
    def update_pagina(self, capitulo: Capitulo, number: int, data: Dict[str, Any]) -> Pagina:
        """Atualiza uma página existente."""
        try:
            pagina = self.get_pagina(capitulo, number)
            for key, value in data.items():
                setattr(pagina, key, value)
            pagina.save()
            return pagina
        except Exception as e:
            raise Exception(f"Erro ao atualizar página: {str(e)}")
    
    def delete_pagina(self, capitulo: Capitulo, number: int) -> None:
        """Exclui uma página."""
        try:
            pagina = self.get_pagina(capitulo, number)
            pagina.delete()
        except Exception as e:
            raise Exception(f"Erro ao excluir página: {str(e)}") 
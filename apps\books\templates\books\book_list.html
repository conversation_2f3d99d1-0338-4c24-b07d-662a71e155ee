{% extends 'base.html' %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'books/css/book-list.css' %}">
{% endblock %}

{% block extra_js %}
<script src="{% static 'books/js/book-list.js' %}"></script>
{% endblock %}

{% block title %}Livros - Project Nix{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row">
        <div class="col-lg-8">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h2 mb-1 text-sans text-body">
                        <i class="fas fa-book me-2 text-theme-primary"></i>Livros
                    </h1>
                    <p class="text-theme-secondary mb-0 text-body">Explore nossa coleção de livros disponíveis</p>
                </div>
                <div>
                    {% if user.is_authenticated %}
                        {% if user.is_staff or user.is_superuser %}
                            <a href="{% url 'books:book_create' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Novo Livro
                            </a>
                        {% endif %}
                    {% endif %}
                </div>
            </div>

            {% if books %}
                <div class="row g-4">
                    {% for book in books %}
                        <div class="col-lg-6 col-md-6 col-12">
                            <article class="card h-100 shadow-sm book-card">
                                {% if book.cover_image %}
                                <div class="position-relative">
                                    <img src="{{ book.cover_image.url }}" 
                                         class="card-img-top" 
                                         alt="{{ book.title }}" 
                                         style="height: 250px; object-fit: cover;">
                                    <span class="badge bg-primary position-absolute top-0 start-0 m-2">
                                        <i class="fas fa-book me-1"></i>Livro
                                    </span>
                                </div>
                                {% endif %}
                                <div class="card-body d-flex flex-column">
                                    <h5 class="card-title mb-2">
                                        <a href="{{ book.get_absolute_url }}" class="text-decoration-none text-dark">
                                            {{ book.title }}
                                        </a>
                                    </h5>
                                    {% if book.subtitle %}
                                    <h6 class="card-subtitle mb-3 text-muted">{{ book.subtitle }}</h6>
                                    {% endif %}
                                    <p class="card-text flex-grow-1">{{ book.description|truncatewords:40 }}</p>

                                    <div class="mt-auto">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <small class="text-muted">
                                                <i class="fas fa-user me-1"></i>{{ book.author }}
                                            </small>
                                            <small class="text-muted">
                                                <i class="fas fa-calendar me-1"></i>{{ book.published_date|date:"d/m/Y" }}
                                            </small>
                                        </div>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <a href="{{ book.get_absolute_url }}" class="btn btn-primary btn-sm">
                                                <i class="fas fa-book-open me-1"></i>Ver detalhes
                                            </a>
                                            {% if user.is_staff and book.get_edit_url %}
                                            <a href="{{ book.get_edit_url }}" class="btn btn-outline-secondary btn-sm ms-1">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </article>
                        </div>
                    {% endfor %}
                </div>

                {% if is_paginated %}
                    <nav aria-label="Paginação" class="mt-5">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Anterior</a>
                                </li>
                            {% endif %}
                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <li class="page-item active" aria-current="page">
                                        <span class="page-link">{{ num }}</span>
                                    </li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">Próximo</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-book fa-5x text-theme-secondary mb-3"></i>
                    <h3 class="text-theme-secondary text-sans text-body">Nenhum livro encontrado</h3>
                    <p class="text-theme-secondary text-body">Ainda não há livros cadastrados.</p>
                </div>
            {% endif %}
        </div>

        <div class="col-lg-4">
            <div class="card border-0 shadow-sm mb-4 book-list-card">
                <div class="card-header bg-theme-secondary text-theme-light">
                    <h6 class="mb-0"><i class="fas fa-search me-2"></i>Buscar Livros</h6>
                </div>
                <div class="card-body">
                    <form method="get" action="#" aria-label="Formulário de busca" role="form">
                        <div class="input-group">
                            <input type="text" name="q" class="form-control" placeholder="Digite sua busca...">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="card border-0 shadow-sm mb-4 book-list-card">
                <div class="card-header bg-theme-info text-theme-light">
                    <h6 class="mb-0"><i class="fas fa-folder me-2"></i>Categorias</h6>
                </div>
                <div class="card-body">
                    <ul class="list-group mb-4">
                        {% if categories %}
                            {% for cat in categories %}
                                <a href="{{ cat.get_absolute_url }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center {% if category and cat.pk == category.pk %}active{% endif %}">
                                    {% if cat.icon %}<i class="{{ cat.icon }} me-2"></i>{% endif %}
                                    {{ cat.name }}
                                    <span class="badge bg-theme-primary rounded-pill">{{ cat.books.count }}</span>
                                </a>
                            {% endfor %}
                        {% else %}
                            <li class="list-group-item text-center text-muted">Nenhuma categoria cadastrada.</li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
# Pages

## Responsabilidade
Gerencia páginas estáticas, navegação, SEO e formulários de contato.

## Estrutura
- `models/`: Modelos de página, navegação, SEO.
- `views/`: Views para home, páginas, contato.
- `forms/`: Formulários de contato.
- `services/`: Lógica de navegação, SEO, páginas.
- `repositories/`: Acesso a dados de páginas e navegação.
- `templates/`: Templates HTML de páginas e includes globais.

## Integração
- Integra com `articles` para exibir artigos em páginas.
- Usa includes globais para navbar, footer, etc.

## Pontos de atenção
- SEO e performance.
- Estrutura de navegação e links internos. 
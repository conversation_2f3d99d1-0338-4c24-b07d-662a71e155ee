# Generated by Django 5.2.4 on 2025-07-28 04:49

import apps.mangas.models.base
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mangas', '0002_capitulo_is_published_capitulo_views_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Volume',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Atualizado em')),
                ('number', models.PositiveIntegerField(help_text='Número sequencial do volume', verbose_name='Número do Volume')),
                ('title', models.CharField(blank=True, help_text='Título opcional do volume', max_length=200, verbose_name='Título')),
                ('cover_image', models.ImageField(blank=True, help_text='Capa do volume', null=True, upload_to='mangas/volumes/covers/%Y/%m/%d/', verbose_name='Capa')),
                ('is_published', models.BooleanField(default=True, help_text='Se o volume está visível publicamente', verbose_name='Publicado?')),
            ],
            options={
                'verbose_name': 'Volume',
                'verbose_name_plural': 'Volumes',
                'ordering': ['manga', 'number'],
            },
            bases=(apps.mangas.models.base.SlugMixin, models.Model),
        ),
        migrations.RemoveIndex(
            model_name='capitulo',
            name='chapter_slug_idx',
        ),
        migrations.RemoveIndex(
            model_name='capitulo',
            name='chapter_number_idx',
        ),
        migrations.RemoveIndex(
            model_name='capitulo',
            name='chapter_published_idx',
        ),
        migrations.AddIndex(
            model_name='pagina',
            index=models.Index(fields=['capitulo'], name='page_chapter_idx'),
        ),
        migrations.AddField(
            model_name='volume',
            name='manga',
            field=models.ForeignKey(help_text='Mangá ao qual este volume pertence', on_delete=django.db.models.deletion.CASCADE, related_name='volumes', to='mangas.manga', verbose_name='Mangá'),
        ),
        migrations.AlterUniqueTogether(
            name='capitulo',
            unique_together=set(),
        ),
        migrations.AddField(
            model_name='capitulo',
            name='volume',
            field=models.ForeignKey(default=1, help_text='Volume ao qual este capítulo pertence', on_delete=django.db.models.deletion.CASCADE, related_name='capitulos', to='mangas.volume', verbose_name='Volume'),
            preserve_default=False,
        ),
        migrations.AlterUniqueTogether(
            name='capitulo',
            unique_together={('volume', 'number')},
        ),
        migrations.AddIndex(
            model_name='volume',
            index=models.Index(fields=['manga', 'number'], name='mangas_volu_manga_i_03aef1_idx'),
        ),
        migrations.AddIndex(
            model_name='volume',
            index=models.Index(fields=['is_published'], name='mangas_volu_is_publ_e1824e_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='volume',
            unique_together={('manga', 'number')},
        ),
        migrations.RemoveField(
            model_name='capitulo',
            name='manga',
        ),
    ]

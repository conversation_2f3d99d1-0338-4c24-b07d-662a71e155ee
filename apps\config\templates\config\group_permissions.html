{% extends 'base.html' %}
{% load static %}

{% block title %}Gerenciar Grupos - Project Nix{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h2 mb-0">
                <i class="fas fa-layer-group text-primary"></i>
                Gerenciar Grupos
            </h1>
            <p class="text-muted">Visualize e gerencie grupos de usuários</p>
        </div>
    </div>

    <!-- Filtros -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-6">
                            <input type="text" name="search" class="form-control" 
                                   placeholder="Buscar por nome do grupo..." 
                                   value="{{ request.GET.search }}">
                        </div>
                        <div class="col-md-4">
                            <select name="order_by" class="form-select">
                                <option value="-user_count" {% if request.GET.order_by == '-user_count' %}selected{% endif %}>Mais membros</option>
                                <option value="user_count" {% if request.GET.order_by == 'user_count' %}selected{% endif %}>Menos membros</option>
                                <option value="name" {% if request.GET.order_by == 'name' %}selected{% endif %}>Nome A-Z</option>
                                <option value="-name" {% if request.GET.order_by == '-name' %}selected{% endif %}>Nome Z-A</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search"></i> Filtrar
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Estatísticas -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ total_groups }}</h4>
                            <small>Total de Grupos</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-layer-group fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ total_users }}</h4>
                            <small>Total de Usuários</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Lista de Grupos -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Lista de Grupos</h5>
                    <a href="{% url 'config:group_create' %}" class="btn btn-success btn-sm">
                        <i class="fas fa-plus"></i> Novo Grupo
                    </a>
                </div>
                <div class="card-body">
                    {% if groups %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Nome do Grupo</th>
                                        <th>Membros</th>
                                        <th>Permissões</th>
                                        <th>Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for group in groups %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="bg-primary rounded-circle me-2 d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                                                    <i class="fas fa-layer-group text-white"></i>
                                                </div>
                                                <div>
                                                    <strong>{{ group.name }}</strong>
                                                    {% if group.name|lower == 'administrador' or group.name|lower == 'admin' %}
                                                        <span class="badge bg-danger ms-1">Admin</span>
                                                    {% elif group.name|lower == 'editor' %}
                                                        <span class="badge bg-warning ms-1">Editor</span>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ group.user_count }} membros</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ group.permissions.count }} permissões</span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{% url 'config:group_detail' group.slug %}" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{% url 'config:group_update' group.slug %}" 
                                                   class="btn btn-sm btn-outline-warning">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'config:group_delete' group.slug %}" 
                                                   class="btn btn-sm btn-outline-danger"
                                                   onclick="return confirm('Tem certeza que deseja excluir este grupo?')">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Paginação -->
                        {% if is_paginated %}
                        <nav aria-label="Paginação de grupos">
                            <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.order_by %}&order_by={{ request.GET.order_by }}{% endif %}">
                                            <i class="fas fa-angle-double-left"></i>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.order_by %}&order_by={{ request.GET.order_by }}{% endif %}">
                                            <i class="fas fa-angle-left"></i>
                                        </a>
                                    </li>
                                {% endif %}

                                <li class="page-item active">
                                    <span class="page-link">
                                        Página {{ page_obj.number }} de {{ page_obj.paginator.num_pages }}
                                    </span>
                                </li>

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.order_by %}&order_by={{ request.GET.order_by }}{% endif %}">
                                            <i class="fas fa-angle-right"></i>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.order_by %}&order_by={{ request.GET.order_by }}{% endif %}">
                                            <i class="fas fa-angle-double-right"></i>
                                        </a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-layer-group fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Nenhum grupo encontrado</h5>
                            <p class="text-muted">Tente ajustar os filtros de busca ou criar um novo grupo.</p>
                            <a href="{% url 'config:group_create' %}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Criar Primeiro Grupo
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 
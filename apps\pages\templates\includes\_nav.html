{% load static %}

<nav class="navbar navbar-expand-lg navbar-django">
    <div class="container">
        <!-- Brand -->
        <a class="navbar-brand" href="{% url 'pages:home' %}">
            <img src="/static/favicon.ico" alt="Project Nix Logo" width="32" height="32" class="me-2">
            Project Nix
        </a>
        
        <!-- Mobile Toggle -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <!-- Navigation Links -->
        <div class="collapse navbar-collapse position-relative" id="navbarNav">
            <!-- Botão X para fechar no mobile -->
            <button type="button" class="navbar-close-x d-lg-none" aria-label="Fechar menu" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <i class="fas fa-times"></i>
            </button>
            <!-- Main Navigation -->
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.url_name == 'home' %}active{% endif %}" href="{% url 'pages:home' %}">
                        <i class="fas fa-home me-1"></i>Home
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if 'articles' in request.resolver_match.namespace %}active{% endif %}" href="{% url 'articles:article_list' %}">
                        <i class="fas fa-newspaper me-1"></i>Artigos
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if 'books' in request.resolver_match.namespace %}active{% endif %}" href="#">
                        <i class="fas fa-book me-1"></i>Livros
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if 'mangas' in request.resolver_match.namespace %}active{% endif %}" href="#">
                        <i class="fas fa-book-open me-1"></i>Mangás
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if 'audiobooks' in request.resolver_match.namespace %}active{% endif %}" href="#">
                        <i class="fas fa-headphones me-1"></i>Audiolivros
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.url_name == 'about' %}active{% endif %}" href="{% url 'pages:about' %}">
                        <i class="fas fa-info-circle me-1"></i>Sobre
                    </a>
                </li>

            </ul>
            
            <!-- Search Form -->
            <form class="d-flex me-3 form-django" method="get" action="{% url 'articles:search' %}" aria-label="Formulário de busca" role="form">
                <div class="input-group">
                    <input class="form-control form-control-enhanced form-control-sm" type="search" name="q" placeholder="Buscar..."
                           aria-label="Campo de busca" aria-describedby="search-button" value="{{ request.GET.q }}">
                    <button class="btn btn-outline-light btn-sm text-sans" type="submit"
                            id="search-button" aria-label="Buscar">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
            
            <!-- User Menu -->
            <ul class="navbar-nav align-items-center">
                <!-- Theme Toggle -->
                <li class="nav-item me-2">
                    <div class="theme-toggle" role="radiogroup" aria-label="Escolher tema">
                        <button class="theme-option" data-theme="light" title="Tema claro" aria-label="Tema claro" role="radio">
                            <i class="fas fa-sun"></i>
                        </button>
                        <button class="theme-option" data-theme="dark" title="Tema escuro" aria-label="Tema escuro" role="radio">
                            <i class="fas fa-moon"></i>
                        </button>
                    </div>
                </li>

                {% if user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" role="button"
                           data-bs-toggle="dropdown" aria-expanded="false" aria-label="Menu do usuário">
                            <div class="avatar-sm me-2">
                                <div class="bg-django-green rounded-circle d-flex align-items-center justify-content-center" style="width: 24px; height: 24px;">
                                    <i class="fas fa-user text-white small"></i>
                                </div>
                            </div>
                            <span class="d-none d-md-inline">{{ user.get_full_name|default:user.username }}</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <h6 class="dropdown-header text-sans text-body">
                                    <i class="fas fa-user me-2"></i>{{ user.email }}
                                </h6>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            
                            <!-- Profile -->
                            {% if request.resolver_match.url_name == 'profile' %}
                            <li><span class="dropdown-item active bg-django-green text-theme-light">
                                <i class="fas fa-user-circle me-2"></i>Meu Perfil (atual)
                            </span></li>
                            {% else %}
                            <li><a class="dropdown-item" href="{% url 'accounts:profile' %}">
                                <i class="fas fa-user-circle me-2"></i>Meu Perfil
                            </a></li>
                            {% endif %}
                            
                            <!-- Admin Links -->
                            {% if user.is_staff %}
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <h6 class="dropdown-header text-django-green text-sans text-body">
                                        <i class="fas fa-cog me-2"></i>Administração
                                    </h6>
                                </li>
                                <li><a class="dropdown-item" href="{% url 'config:dashboard' %}">
                                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                                </a></li>
                                <li><a class="dropdown-item" href="/admin/">
                                    <i class="fas fa-tools me-2"></i>Django Admin
                                </a></li>
                            {% endif %}
                            
                            <li><hr class="dropdown-divider"></li>
                            
                            <!-- Settings -->
                            {% if request.resolver_match.url_name == 'settings' %}
                            <li><span class="dropdown-item active bg-django-green text-theme-light">
                                <i class="fas fa-cog me-2"></i>Configurações (atual)
                            </span></li>
                            {% else %}
                            <li><a class="dropdown-item" href="{% url 'accounts:settings' %}">
                                <i class="fas fa-cog me-2"></i>Configurações
                            </a></li>
                            {% endif %}
                            
                            <!-- Logout -->
                            <li><a class="dropdown-item text-theme-danger" href="{% url 'accounts:logout' %}">
                                <i class="fas fa-sign-out-alt me-2"></i>Sair
                            </a></li>
                        </ul>
                    </li>
                {% else %}
                    <!-- Guest Menu -->
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'accounts:login' %}">
                            <i class="fas fa-sign-in-alt me-1"></i>Entrar
                        </a>
                    </li>
                   <!--  <li class="nav-item">
                        <a class="btn btn-outline-light btn-sm ms-2 btn-enhanced" href="{% url 'accounts:register' %}">
                            <i class="fas fa-user-plus me-1"></i>Registrar
                        </a>
                    </li> -->
                {% endif %}
            </ul>
        </div>
    </div>
</nav>

<!-- Breadcrumbs -->
{% if breadcrumbs %}
<nav aria-label="breadcrumb" class="bg-theme-secondary border-bottom">
    <div class="container">
        <ol class="breadcrumb mb-0 py-2">
            {% for breadcrumb in breadcrumbs %}
                {% if breadcrumb.is_current %}
                    <li class="breadcrumb-item active" aria-current="page">
                        {{ breadcrumb.title }}
                    </li>
                {% else %}
                    <li class="breadcrumb-item">
                        <a href="{{ breadcrumb.url }}" class="text-decoration-none">
                            {{ breadcrumb.title }}
                        </a>
                    </li>
                {% endif %}
            {% endfor %}
        </ol>
    </div>
</nav>
{% endif %}

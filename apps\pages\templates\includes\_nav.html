{% load static %}

<!-- Main Navigation -->
<nav class="main-navbar sticky-top" role="navigation" aria-label="Navegação principal">
    <div class="container">
        <!-- Mobile Menu Toggle -->
        <button class="mobile-menu-toggle d-lg-none" type="button" data-bs-toggle="collapse" data-bs-target="#mainNav">
            <i class="fas fa-bars"></i>
        </button>

        <!-- Mobile Brand Text -->
        <div class="mobile-brand d-lg-none">
            <span class="brand-text">Project Nix</span>
        </div>

        <!-- Navigation Menu -->
        <div class="navbar-collapse" id="mainNav">
            <!-- Close Button for Mobile -->
            <button class="mobile-close d-lg-none" type="button" data-bs-toggle="collapse" data-bs-target="#mainNav">
                <i class="fas fa-times"></i>
            </button>

            <!-- Main Navigation Menu -->
            <ul class="navbar-nav w-100 justify-content-center desktop-menu">
                <!-- Home -->
                <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.url_name == 'home' %}active{% endif %}" href="{% url 'pages:home' %}">
                        <i class="fas fa-home me-1"></i>Home
                    </a>
                </li>

                <!-- Artigos -->
                <li class="nav-item">
                    <a class="nav-link {% if 'articles' in request.resolver_match.namespace %}active{% endif %}" href="{% url 'articles:article_list' %}">
                        <i class="fas fa-newspaper me-1"></i>Artigos
                    </a>
                </li>

                <!-- Livros -->
                <li class="nav-item">
                    <a class="nav-link {% if 'books' in request.resolver_match.namespace %}active{% endif %}" href="{% url 'books:book_list' %}">
                        <i class="fas fa-book me-1"></i>Livros
                    </a>
                </li>

                <!-- Mangás -->
                <li class="nav-item">
                    <a class="nav-link {% if 'mangas' in request.resolver_match.namespace %}active{% endif %}" href="{% url 'mangas:manga_list' %}">
                        <i class="fas fa-book-open me-1"></i>Mangás
                    </a>
                </li>

                <!-- Audiolivros -->
                <li class="nav-item">
                    <a class="nav-link {% if 'audiobooks' in request.resolver_match.namespace %}active{% endif %}" href="{% url 'audiobooks:audiobook_list' %}">
                        <i class="fas fa-headphones me-1"></i>Audiolivros
                    </a>
                </li>

                <!-- Sobre -->
                <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.url_name == 'about' %}active{% endif %}" href="{% url 'pages:about' %}">
                        <i class="fas fa-info-circle me-1"></i>Sobre
                    </a>
                </li>
                {% if not user.is_authenticated %}
                <li class="nav-item">
                    <a href="{% url 'accounts:login' %}" class="nav-link">
                        <i class="fas fa-sign-in-alt"></i>Entrar
                    </a>
                </li>
                {% endif %}
                {% if user.is_authenticated %}
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <img src="{{ user.get_avatar_url }}" alt="Avatar" class="navbar-avatar me-2" style="width:32px; height:32px; object-fit:cover; border-radius:50%; border:2px solid #eee; background:#fff;">
                        <span>{{ user.get_full_name|default:user.username }}</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li>
                            <a class="dropdown-item" href="{% url 'accounts:profile' %}">
                                <i class="fas fa-user-circle me-1"></i>Meu Perfil
                            </a>
                        </li>
                        {% if user.is_staff %}
                        <li>
                            <a class="dropdown-item" href="{% url 'config:dashboard' %}">
                                <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                            </a>
                        </li>
                        {% endif %}
                        <li>
                            <a class="dropdown-item" href="{% url 'accounts:settings' %}">
                                <i class="fas fa-cog me-1"></i>Configurações
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item text-danger" href="{% url 'accounts:logout' %}">
                                <i class="fas fa-sign-out-alt me-1"></i>Sair
                            </a>
                        </li>
                    </ul>
                </li>
                {% endif %}
            </ul>


            <!-- User Menu Mobile -->
            <div class="mobile-user-menu d-lg-none mt-4">
                {% if user.is_authenticated %}
                    <div class="user-info text-center mb-3">
                        <div class="user-avatar mb-2">
                            <img src="{{ user.get_avatar_url }}" alt="Avatar" class="navbar-avatar" style="width:64px; height:64px; object-fit:cover; border-radius:50%; border:2px solid #eee; background:#fff;" onerror="this.onerror=null;this.src='https://ui-avatars.com/api/?name={{ user.get_full_name|default:user.username }}&size=200&background=007bff&color=fff&bold=true';">
                        </div>
                        <div class="user-name">{{ user.get_full_name|default:user.username }}</div>
                        <div class="user-email">{{ user.email }}</div>
                    </div>
                    <div class="user-actions">
                        <a href="{% url 'accounts:profile' %}" class="mobile-menu-item">
                            <i class="fas fa-user-circle"></i>Meu Perfil
                        </a>
                        {% if user.is_staff %}
                            <a href="{% url 'config:dashboard' %}" class="mobile-menu-item">
                                <i class="fas fa-tachometer-alt"></i>Dashboard
                            </a>
                        {% endif %}
                        <a href="{% url 'accounts:settings' %}" class="mobile-menu-item">
                            <i class="fas fa-cog"></i>Configurações
                        </a>
                        <a href="{% url 'accounts:logout' %}" class="mobile-menu-item logout">
                            <i class="fas fa-sign-out-alt"></i>Sair
                        </a>
                    </div>
                {% else %}
                    <div class="guest-actions">
                        <a href="{% url 'accounts:login' %}" class="mobile-menu-item">
                            <i class="fas fa-sign-in-alt"></i>Entrar
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</nav>

<!-- Top Header -->
<header class="top-header">
    <div class="container">
        <div class="row align-items-center py-3">
            <!-- Brand Text -->
            <div class="col-12 col-md-4 text-center text-md-start mb-3 mb-md-0">
                <a href="{% url 'pages:home' %}" class="brand-logo">
                    <span class="brand-text">Project Nix</span>
                </a>
            </div>

            <!-- Search Bar Central -->
            <div class="col-12 col-md-4 mb-3 mb-md-0">
                <form class="search-form" method="get" action="{% url 'articles:search' %}">
                    <div class="search-wrapper">
                        <input type="search" name="q" placeholder="Busque aqui..."
                               class="search-input" value="{{ request.GET.q }}">
                        <button type="submit" class="search-btn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>

            <!-- Theme Toggle -->
            <div class="col-12 col-md-4 text-center text-md-end">
                <div class="header-actions">
                    <!-- Theme Toggle -->
                    <div class="theme-toggle d-inline-flex">
                        <button class="theme-option" data-theme="light" title="Tema claro">
                            <i class="fas fa-sun"></i>
                        </button>
                        <button class="theme-option" data-theme="dark" title="Tema escuro">
                            <i class="fas fa-moon"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>



<!-- Breadcrumbs -->
{% if breadcrumbs %}
<nav aria-label="breadcrumb" class="breadcrumb-nav">
    <div class="container">
        <ol class="breadcrumb mb-0 py-2">
            {% for breadcrumb in breadcrumbs %}
                {% if breadcrumb.is_current %}
                    <li class="breadcrumb-item active" aria-current="page">
                        {{ breadcrumb.title }}
                    </li>
                {% else %}
                    <li class="breadcrumb-item">
                        <a href="{{ breadcrumb.url }}">{{ breadcrumb.title }}</a>
                    </li>
                {% endif %}
            {% endfor %}
        </ol>
    </div>
</nav>
{% endif %}

{% extends 'config/base_config.html' %}
{% block config_title %}Módulos do Sistema{% endblock %}
{% block config_content %}
<div class="d-flex justify-content-between align-items-center mb-4 mt-4">
    <h1 class="h3 config-page-title">
        <i class="fas fa-cubes me-2"></i>Módulos do Sistema
    </h1>
</div>
<div class="card shadow-sm">
    <div class="card-body p-0">
        <div class="table-responsive d-none d-md-block">
            <table class="table table-hover align-middle mb-0">
                <thead class="table-light">
                    <tr>
                        <th scope="col"><i class="fas fa-cube"></i> Nome</th>
                        <th scope="col"><i class="fas fa-align-left"></i> Descrição</th>
                        <th scope="col"><i class="fas fa-toggle-on"></i> Ativo</th>
                        <th scope="col"><i class="fas fa-cogs"></i> Ações</th>
                    </tr>
                </thead>
                <tbody>
                {% for module in modules %}
                    <tr{% if module.is_core %} class="table-success"{% endif %}>
                        <td>
                            <span class="fw-bold">{{ module.display_name }}</span>
                            {% if module.is_core %}
                                <span class="badge bg-success ms-2" title="Módulo essencial"><i class="fas fa-star"></i> Core</span>
                            {% endif %}
                        </td>
                        <td>{{ module.description|default:'—' }}</td>
                        <td>
                            {% if module.is_enabled %}
                                <span class="badge bg-success"><i class="fas fa-check-circle"></i> Ativo</span>
                            {% else %}
                                <span class="badge bg-secondary"><i class="fas fa-times-circle"></i> Inativo</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{% url 'config:module_detail' module.app_name %}" class="btn btn-outline-info" title="Detalhes"><i class="fas fa-info-circle"></i></a>
                                {% if not module.is_core %}
                                    {% if not module.is_enabled %}
                                        <form action="{% url 'config:module_enable' module.app_name %}" method="post" style="display:inline;">
                                            {% csrf_token %}
                                            <button type="submit" class="btn btn-outline-success" title="Ativar"><i class="fas fa-toggle-on"></i></button>
                                        </form>
                                    {% else %}
                                        <form action="{% url 'config:module_disable' module.app_name %}" method="post" style="display:inline;">
                                            {% csrf_token %}
                                            <button type="submit" class="btn btn-outline-warning" title="Desativar"><i class="fas fa-toggle-off"></i></button>
                                        </form>
                                    {% endif %}
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                {% empty %}
                    <tr><td colspan="4" class="text-center text-muted">Nenhum módulo encontrado.</td></tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
        <!-- Layout responsivo para mobile -->
        <div class="d-block d-md-none">
            {% for module in modules %}
            <div class="card mb-2 {% if module.is_core %}border-success{% endif %}">
                <div class="card-body py-2 px-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span class="fw-bold">{{ module.display_name }}</span>
                        {% if module.is_core %}
                            <span class="badge bg-success ms-2" title="Módulo essencial"><i class="fas fa-star"></i> Core</span>
                        {% endif %}
                    </div>
                    <div class="mb-1 small text-muted">{{ module.description|default:'—' }}</div>
                    <div class="mb-2">
                        {% if module.is_enabled %}
                            <span class="badge bg-success"><i class="fas fa-check-circle"></i> Ativo</span>
                        {% else %}
                            <span class="badge bg-secondary"><i class="fas fa-times-circle"></i> Inativo</span>
                        {% endif %}
                    </div>
                    <div class="btn-group btn-group-sm w-100" role="group">
                        <a href="{% url 'config:module_detail' module.app_name %}" class="btn btn-outline-info w-100" title="Detalhes"><i class="fas fa-info-circle"></i></a>
                        {% if not module.is_core %}
                            {% if not module.is_enabled %}
                                <form action="{% url 'config:module_enable' module.app_name %}" method="post" style="display:inline;">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-outline-success w-100" title="Ativar"><i class="fas fa-toggle-on"></i></button>
                                </form>
                            {% else %}
                                <form action="{% url 'config:module_disable' module.app_name %}" method="post" style="display:inline;">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-outline-warning w-100" title="Desativar"><i class="fas fa-toggle-off"></i></button>
                                </form>
                            {% endif %}
                        {% endif %}
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="text-center text-muted">Nenhum módulo encontrado.</div>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}

{% extends 'base.html' %}
{% load static %}

{% block title %}{{ audiobook.title }} - Audiolivros - Project Nix{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-4 mb-4">
            {% if audiobook.cover_image %}
            <img src="{{ audiobook.cover_image.url }}" class="img-fluid rounded shadow" 
                 alt="{{ audiobook.title }}">
            {% else %}
            <div class="bg-light rounded shadow d-flex align-items-center justify-content-center" 
                 style="height: 400px;">
                <i class="fas fa-headphones fa-5x text-muted"></i>
            </div>
            {% endif %}
        </div>
        
        <div class="col-md-8">
            <div class="d-flex justify-content-between align-items-start mb-3">
                <h1 class="h2">{{ audiobook.title }}</h1>
                {% if user.is_staff %}
                <div class="btn-group">
                    <a href="{% url 'audiobooks:audiobook_edit' audiobook.slug %}" 
                       class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-edit me-1"></i>Editar
                    </a>
                    <a href="{% url 'audiobooks:audiobook_delete' audiobook.slug %}" 
                       class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-trash me-1"></i>Excluir
                    </a>
                </div>
                {% endif %}
            </div>

            {% if audiobook.author %}
            <p class="text-muted mb-2">
                <i class="fas fa-user me-2"></i>
                <strong>Autor:</strong> {{ audiobook.author }}
            </p>
            {% endif %}

            {% if audiobook.narrator %}
            <p class="text-muted mb-2">
                <i class="fas fa-microphone me-2"></i>
                <strong>Narrador:</strong> {{ audiobook.narrator }}
            </p>
            {% endif %}

            {% if audiobook.duration %}
            <p class="text-muted mb-2">
                <i class="fas fa-clock me-2"></i>
                <strong>Duração:</strong> {{ audiobook.duration }}
            </p>
            {% endif %}

            {% if audiobook.published_date %}
            <p class="text-muted mb-3">
                <i class="fas fa-calendar me-2"></i>
                <strong>Publicado em:</strong> {{ audiobook.published_date|date:"d/m/Y" }}
            </p>
            {% endif %}

            {% if audiobook.description %}
            <div class="mb-4">
                <h5>Descrição</h5>
                <p class="text-justify">{{ audiobook.description|linebreaks }}</p>
            </div>
            {% endif %}

            <!-- Audio Player -->
            {% if audiobook.audio_file %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-play-circle me-2"></i>Player de Áudio
                    </h5>
                </div>
                <div class="card-body">
                    <audio controls class="w-100" preload="metadata">
                        <source src="{{ audiobook.audio_file.url }}" type="audio/mpeg">
                        <source src="{{ audiobook.audio_file.url }}" type="audio/wav">
                        <source src="{{ audiobook.audio_file.url }}" type="audio/ogg">
                        Seu navegador não suporta o elemento de áudio.
                    </audio>
                </div>
            </div>
            {% endif %}

            <!-- Action Buttons -->
            <div class="d-flex gap-2 mb-4">
                {% if audiobook.audio_file %}
                <button class="btn btn-primary" onclick="playAudio()">
                    <i class="fas fa-play me-2"></i>Reproduzir
                </button>
                <button class="btn btn-outline-primary" onclick="pauseAudio()">
                    <i class="fas fa-pause me-2"></i>Pausar
                </button>
                {% endif %}
                
                <button class="btn btn-outline-success" onclick="addToFavorites()">
                    <i class="fas fa-heart me-2"></i>Favoritar
                </button>
                
                <a href="{% url 'audiobooks:audiobook_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Voltar
                </a>
            </div>

            <!-- Progress Bar (if user is logged in) -->
            {% if user.is_authenticated %}
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>Seu Progresso
                    </h6>
                </div>
                <div class="card-body">
                    <div class="progress mb-2">
                        <div class="progress-bar" role="progressbar" style="width: 0%" 
                             aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                            0%
                        </div>
                    </div>
                    <small class="text-muted">
                        Tempo atual: <span id="current-time">00:00:00</span>
                    </small>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function playAudio() {
    const audio = document.querySelector('audio');
    if (audio) {
        audio.play();
    }
}

function pauseAudio() {
    const audio = document.querySelector('audio');
    if (audio) {
        audio.pause();
    }
}

function addToFavorites() {
    // Implementar funcionalidade de favoritos via AJAX
    alert('Funcionalidade de favoritos será implementada em breve!');
}

// Update progress bar
document.addEventListener('DOMContentLoaded', function() {
    const audio = document.querySelector('audio');
    const progressBar = document.querySelector('.progress-bar');
    const currentTimeSpan = document.getElementById('current-time');
    
    if (audio && progressBar) {
        audio.addEventListener('timeupdate', function() {
            const progress = (audio.currentTime / audio.duration) * 100;
            progressBar.style.width = progress + '%';
            progressBar.setAttribute('aria-valuenow', progress);
            progressBar.textContent = Math.round(progress) + '%';
            
            if (currentTimeSpan) {
                const minutes = Math.floor(audio.currentTime / 60);
                const seconds = Math.floor(audio.currentTime % 60);
                currentTimeSpan.textContent = 
                    `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}:00`;
            }
        });
    }
});
</script>
{% endblock %}

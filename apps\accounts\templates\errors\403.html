{% extends 'base.html' %}
{% load static %}

{% block title %}Acesso Negado - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid min-vh-100 d-flex align-items-center justify-content-center">
    <div class="row justify-content-center w-100">
        <div class="col-lg-6 col-md-8 col-sm-10">
            <div class="text-center">
                <!-- Ícone de erro -->
                <div class="mb-4">
                    <i class="fas fa-ban text-danger" style="font-size: 6rem;"></i>
                </div>
                
                <!-- Título -->
                <h1 class="display-1 fw-bold text-danger mb-3">403</h1>
                <h2 class="h3 mb-4">Acesso Negado</h2>
                
                <!-- Mensagem -->
                <div class="mb-4">
                    <p class="lead text-muted">
                        Você não tem permissão para acessar {{ area_name }}.
                    </p>
                    <p class="text-muted">
                        Esta área requer permissões especiais. Entre em contato com um administrador se precisar de acesso.
                    </p>
                </div>
                
                <!-- Ações -->
                <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center mb-5">
                    <a href="{% url 'pages:home' %}" class="btn btn-primary btn-lg">
                        <i class="fas fa-home me-2"></i>Voltar ao Início
                    </a>
                    {% if user.is_authenticated %}
                        <a href="{% url 'accounts:profile' %}" class="btn btn-outline-secondary btn-lg">
                            <i class="fas fa-user me-2"></i>Meu Perfil
                        </a>
                    {% else %}
                        <a href="{% url 'accounts:login' %}" class="btn btn-outline-secondary btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>Fazer Login
                        </a>
                    {% endif %}
                </div>
                
                <!-- Links úteis -->
                <div class="row g-3">
                    <div class="col-md-4">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center">
                                <img src="/static/favicon-32x32.png" alt="FireFlies Logo" width="32" height="32" class="mb-3">
                                <h5 class="card-title">FireFlies</h5>
                                <p class="card-text text-muted">Sistema de gerenciamento de conteúdo</p>
                                <a href="{% url 'pages:home' %}" class="btn btn-outline-primary btn-sm">
                                    Conhecer
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="fas fa-newspaper text-info mb-3" style="font-size: 2rem;"></i>
                                <h5 class="card-title">Artigos</h5>
                                <p class="card-text text-muted">Confira nossos artigos públicos</p>
                                <a href="{% url 'articles:article_list' %}" class="btn btn-outline-info btn-sm">
                                    Ver Artigos
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="fas fa-envelope text-success mb-3" style="font-size: 2rem;"></i>
                                <h5 class="card-title">Contato</h5>
                                <p class="card-text text-muted">Entre em contato conosco</p>
                                <a href="{% url 'pages:contact' %}" class="btn btn-outline-success btn-sm">
                                    Falar Conosco
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Estilos personalizados -->
<style>
.text-danger {
    color: #dc3545 !important;
}

.btn-primary {
    background-color: #0C4B33;
    border-color: #0C4B33;
}

.btn-primary:hover {
    background-color: #44B78B;
    border-color: #44B78B;
}

.btn-outline-primary {
    color: #0C4B33;
    border-color: #0C4B33;
}

.btn-outline-primary:hover {
    background-color: #0C4B33;
    border-color: #0C4B33;
}

.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-5px);
}

/* Dark theme support */
[data-theme="dark"] .card {
    background-color: var(--bs-dark);
    border-color: var(--bs-gray-700);
}

[data-theme="dark"] .text-muted {
    color: var(--bs-gray-400) !important;
}

[data-theme="dark"] .btn-outline-secondary {
    color: var(--bs-gray-300);
    border-color: var(--bs-gray-600);
}

[data-theme="dark"] .btn-outline-secondary:hover {
    background-color: var(--bs-gray-600);
    border-color: var(--bs-gray-600);
    color: white;
}
</style>
{% endblock %} 
# Generated by Django 5.2.2 on 2025-07-02 18:05

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('config', '0005_appmoduleconfiguration'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Group',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Module',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.AlterModelOptions(
            name='useractivitylog',
            options={'ordering': ['-timestamp'], 'verbose_name': 'log de atividade', 'verbose_name_plural': 'logs de atividade'},
        ),
        migrations.RemoveIndex(
            model_name='useractivitylog',
            name='config_user_user_id_deeb8b_idx',
        ),
        migrations.RemoveIndex(
            model_name='useractivitylog',
            name='config_user_action_a6f429_idx',
        ),
        migrations.RemoveIndex(
            model_name='useractivitylog',
            name='config_user_target__739c91_idx',
        ),
        migrations.RemoveField(
            model_name='useractivitylog',
            name='created_at',
        ),
        migrations.RemoveField(
            model_name='useractivitylog',
            name='description',
        ),
        migrations.RemoveField(
            model_name='useractivitylog',
            name='extra_data',
        ),
        migrations.RemoveField(
            model_name='useractivitylog',
            name='ip_address',
        ),
        migrations.RemoveField(
            model_name='useractivitylog',
            name='target_user',
        ),
        migrations.RemoveField(
            model_name='useractivitylog',
            name='user_agent',
        ),
        migrations.AddField(
            model_name='useractivitylog',
            name='details',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='useractivitylog',
            name='timestamp',
            field=models.DateTimeField(auto_now_add=True, default='2024-07-02T12:00:00'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='useractivitylog',
            name='action',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='useractivitylog',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='useractivitylog',
            index=models.Index(fields=['user', '-timestamp'], name='config_user_user_id_4872f1_idx'),
        ),
        migrations.AddIndex(
            model_name='useractivitylog',
            index=models.Index(fields=['action', '-timestamp'], name='config_user_action_74710d_idx'),
        ),
        migrations.AddField(
            model_name='module',
            name='dependencies',
            field=models.ManyToManyField(blank=True, related_name='dependents', to='config.module'),
        ),
    ]

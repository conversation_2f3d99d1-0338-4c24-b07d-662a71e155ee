/* ===== FORMULÁRIOS ACESSÍVEIS - PROJECT NIX ===== */

/* Validação de formulários */
.was-validated .form-control:valid {
    border-color: var(--nix-success);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23065f46' d='m2.3 6.73.94-.94 1.38 1.38 3.22-3.22.94.94-4.16 4.16z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.was-validated .form-control:invalid {
    border-color: var(--nix-danger);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23991b1b'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4 1.4-1.4M8.6 6l-1.4 1.4 1.4 1.4'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* Feedback de validação personalizado */
.valid-feedback {
    display: block;
    color: var(--nix-success);
    font-size: 0.875rem;
    margin-top: 0.25rem;
    font-weight: 500;
}

.invalid-feedback {
    display: block;
    color: var(--nix-danger);
    font-size: 0.875rem;
    margin-top: 0.25rem;
    font-weight: 500;
}

/* Estilos para campos de formulário */
.form-control {
    transition: var(--transition-fast);
    background-color: var(--bg-color);
    border-color: var(--border-color);
    color: var(--text-color);
}

.form-control:focus {
    border-color: var(--focus-ring);
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 0.25rem rgba(124, 58, 237, 0.25);
    background-color: var(--bg-color);
}

/* Labels flutuantes */
.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    opacity: 0.65;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

/* Grupos de formulário */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #495057;
}

/* Indicadores de força de senha */
.password-strength {
    margin-top: 0.5rem;
}

.password-strength-bar {
    height: 4px;
    background-color: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 0.25rem;
}

.password-strength-fill {
    height: 100%;
    transition: width 0.3s ease, background-color 0.3s ease;
    border-radius: 2px;
}

.password-strength-weak .password-strength-fill {
    width: 25%;
    background-color: #dc3545;
}

.password-strength-fair .password-strength-fill {
    width: 50%;
    background-color: #fd7e14;
}

.password-strength-good .password-strength-fill {
    width: 75%;
    background-color: #ffc107;
}

.password-strength-strong .password-strength-fill {
    width: 100%;
    background-color: #198754;
}

.password-strength-text {
    font-size: 0.75rem;
    color: #6c757d;
}

/* Contador de caracteres */
.char-counter {
    font-size: 0.75rem;
    color: #6c757d;
    text-align: right;
    margin-top: 0.25rem;
}

.char-counter.warning {
    color: #fd7e14;
}

.char-counter.danger {
    color: #dc3545;
}

/* Upload de arquivos */
.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.375rem;
    padding: 2rem;
    text-align: center;
    transition: border-color 0.15s ease-in-out, background-color 0.15s ease-in-out;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: #86b7fe;
    background-color: #f8f9fa;
}

.file-upload-area.dragover {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.1);
}

.file-upload-icon {
    font-size: 2rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.file-upload-text {
    color: #495057;
    margin-bottom: 0.5rem;
}

.file-upload-hint {
    font-size: 0.875rem;
    color: #6c757d;
}

/* Preview de imagem */
.image-preview {
    position: relative;
    display: inline-block;
    margin-top: 1rem;
}

.image-preview img {
    max-width: 200px;
    max-height: 200px;
    border-radius: 0.375rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.image-preview-remove {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    font-size: 0.75rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-preview-remove:hover {
    background-color: #bb2d3b;
}

/* Botões de formulário */
.form-actions {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #dee2e6;
}

.btn-form-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 0.375rem;
    transition: all 0.15s ease-in-out;
}

.btn-form-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(13, 110, 253, 0.25);
}

/* Animações */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.form-control.error {
    animation: shake 0.5s ease-in-out;
}

/* Loading states */
.btn-loading {
    position: relative;
    pointer-events: none;
}

.btn-loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsividade */
@media (max-width: 576px) {
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .btn-form-primary {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .form-actions {
        text-align: center;
    }
    
    .image-preview img {
        max-width: 150px;
        max-height: 150px;
    }
}

/* Melhorias de acessibilidade */
.form-control:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Tooltips para formulários */
.form-tooltip {
    position: relative;
    display: inline-block;
    margin-left: 0.25rem;
}

.form-tooltip .tooltip-text {
    visibility: hidden;
    width: 200px;
    background-color: #333;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px 8px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 0.75rem;
}

.form-tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/**
 * Estilos Avançados para o Leitor de Mangá
 * Suporte a múltiplos modos de leitura e funcionalidades modernas
 */

/* Container Principal do Leitor */
.manga-reader {
    position: relative;
    width: 100%;
    min-height: 100vh;
    background: #1a1a1a;
    overflow: hidden;
}

/* Modos de Leitura */
.manga-reader.mode-vertical {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 20px 0;
}

.manga-reader.mode-horizontal {
    display: flex;
    flex-direction: row;
    overflow-x: auto;
    gap: 10px;
    padding: 20px;
    scroll-snap-type: x mandatory;
}

.manga-reader.mode-webtoon {
    display: block;
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

/* Container de Páginas */
#pages-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

/* Páginas Individuais */
.manga-page {
    position: relative;
    margin: 0 auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    transition: all 0.3s ease;
}

.manga-page img {
    width: 100%;
    height: auto;
    display: block;
    object-fit: contain;
    transition: transform 0.3s ease;
}

/* Modo Horizontal - Scroll Snap */
.mode-horizontal .manga-page {
    flex: 0 0 auto;
    width: 90vw;
    max-width: 800px;
    scroll-snap-align: center;
}

/* Modo Webtoon */
.mode-webtoon .manga-page {
    width: 100%;
    margin-bottom: 20px;
}

/* Controles do Leitor */
.reader-controls-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 1000;
    transition: opacity 0.3s ease;
    opacity: 1;
}

.reader-controls-bar.hidden {
    opacity: 0;
    pointer-events: none;
}

.controls-left,
.controls-center,
.controls-right {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Indicador de Página */
.page-indicator {
    color: #fff;
    font-weight: bold;
    font-size: 14px;
    min-width: 80px;
    text-align: center;
}

/* Botões de Controle */
.reader-controls-bar .btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.2s ease;
    font-size: 12px;
}

.reader-controls-bar .btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.reader-controls-bar .btn.active {
    background: #007bff;
    border-color: #007bff;
}

.reader-controls-bar .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* Seletor de Modo de Leitura */
.reading-mode-selector {
    display: flex;
    gap: 5px;
}

.reading-mode-selector .btn {
    padding: 6px 10px;
    font-size: 11px;
}

/* Controles de Zoom */
.zoom-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.zoom-level {
    color: #fff;
    font-size: 12px;
    min-width: 50px;
    text-align: center;
}

/* Barra de Progresso */
.progress-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: rgba(255, 255, 255, 0.1);
    z-index: 1001;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #00d4ff);
    width: 0%;
    transition: width 0.3s ease;
}

/* Modo Tela Cheia */
.fullscreen-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: #000;
    z-index: 9999;
    display: none;
    overflow: hidden;
}

.fullscreen-container.active {
    display: block;
}

.fullscreen-container .manga-reader {
    height: 100vh;
    padding-top: 60px; /* Espaço para controles */
}

/* Controles em Tela Cheia */
.fullscreen-controls {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 10000;
    color: #fff;
}

.fullscreen-controls .chapter-info {
    font-weight: bold;
    font-size: 16px;
}

.fullscreen-controls .page-info {
    margin: 0 20px;
    font-size: 14px;
}

.fullscreen-controls .nav-buttons {
    display: flex;
    gap: 10px;
}

.fullscreen-controls .nav-buttons button {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #fff;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.fullscreen-controls .nav-buttons button:hover {
    background: rgba(255, 255, 255, 0.2);
}

.fullscreen-controls .nav-buttons button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.fullscreen-controls .close-btn {
    background: none;
    border: none;
    color: #fff;
    font-size: 20px;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: background 0.2s ease;
}

.fullscreen-controls .close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Scroll Automático */
.auto-scroll-indicator {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(0, 123, 255, 0.9);
    color: #fff;
    padding: 10px 15px;
    border-radius: 25px;
    font-size: 12px;
    z-index: 1000;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Notificação de Conclusão */
.completion-notification {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 30px;
    text-align: center;
    color: #fff;
    z-index: 10000;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

.completion-content h3 {
    margin-bottom: 15px;
    color: #00d4ff;
}

.completion-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 20px;
}

.completion-actions .btn {
    padding: 8px 16px;
    border-radius: 6px;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.2s ease;
}

.completion-actions .btn-primary {
    background: #007bff;
    color: #fff;
    border: none;
}

.completion-actions .btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Indicadores de Progresso */
.reading-progress-indicator {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: #fff;
    padding: 10px 15px;
    border-radius: 8px;
    font-size: 12px;
    z-index: 1000;
}

/* Loading States */
.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #fff;
}

.loading-spinner .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Responsividade */
@media (max-width: 768px) {
    .reader-controls-bar {
        padding: 8px 15px;
        flex-wrap: wrap;
        gap: 8px;
    }
    
    .controls-center {
        order: 3;
        width: 100%;
        justify-content: center;
        margin-top: 8px;
    }
    
    .reading-mode-selector,
    .zoom-controls {
        gap: 5px;
    }
    
    .reader-controls-bar .btn {
        padding: 6px 10px;
        font-size: 11px;
    }
    
    .page-indicator {
        font-size: 12px;
        min-width: 60px;
    }
    
    .manga-page {
        border-radius: 4px;
    }
    
    .mode-horizontal .manga-page {
        width: 95vw;
    }
}

@media (max-width: 480px) {
    .fullscreen-controls {
        padding: 10px 15px;
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .fullscreen-controls .chapter-info {
        font-size: 14px;
    }
    
    .fullscreen-controls .nav-buttons {
        gap: 5px;
    }
    
    .fullscreen-controls .nav-buttons button {
        padding: 6px 12px;
        font-size: 12px;
    }
}

/* Animações de Transição */
.manga-page {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Estados de Hover */
.manga-page:hover {
    transform: scale(1.02);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);
}

/* Modo Escuro/Light */
[data-theme="light"] .manga-reader {
    background: #f8f9fa;
}

[data-theme="light"] .manga-page {
    background: #fff;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

[data-theme="light"] .reader-controls-bar {
    background: rgba(255, 255, 255, 0.95);
    color: #333;
}

[data-theme="light"] .reader-controls-bar .btn {
    background: rgba(0, 0, 0, 0.1);
    border-color: rgba(0, 0, 0, 0.2);
    color: #333;
}

[data-theme="light"] .reader-controls-bar .btn:hover {
    background: rgba(0, 0, 0, 0.2);
}

[data-theme="light"] .page-indicator {
    color: #333;
}

/* Acessibilidade */
@media (prefers-reduced-motion: reduce) {
    .manga-page,
    .reader-controls-bar .btn,
    .progress-fill {
        transition: none;
    }
    
    .manga-page:hover {
        transform: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .reader-controls-bar {
        background: #000;
        border-bottom: 2px solid #fff;
    }
    
    .reader-controls-bar .btn {
        border: 2px solid #fff;
        background: #000;
        color: #fff;
    }
    
    .progress-fill {
        background: #fff;
    }
} 
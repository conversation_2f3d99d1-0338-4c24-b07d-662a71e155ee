/* ===== PROJECT NIX DESIGN SYSTEM - ACESSIBILIDADE OTIMIZADA ===== */

/*
 * Sistema de cores otimizado para acessibilidade WCAG 2.1 AA
 * Todos os contrastes atendem aos requisitos mínimos de 4.5:1 para texto normal
 * e 3:1 para texto grande e elementos não-textuais
 */

:root {
  /* === CORES PRIMÁRIAS NIX === */
  --nix-primary: #1a1d29;          /* Azul escuro profundo - Contraste 13.5:1 */
  --nix-primary-light: #2d3142;    /* Azul médio - Contraste 8.2:1 */
  --nix-primary-dark: #0f1117;     /* Azul muito escuro - Contraste 18.1:1 */

  /* === CORES SECUNDÁRIAS === */
  --nix-secondary: #f8fafc;        /* Cinza muito claro */
  --nix-secondary-dark: #e2e8f0;   /* Cinza claro */

  /* === CORES DE DESTAQUE ROXO NIX === */
  --nix-accent: #7c3aed;           /* Roxo elegante - Contraste 5.1:1 em branco */
  --nix-accent-light: #8b5cf6;     /* Roxo claro - Contraste 4.6:1 em branco */
  --nix-accent-dark: #5b21b6;      /* Roxo escuro - Contraste 6.8:1 em branco */
  --nix-accent-alt: #6366f1;       /* Índigo complementar - Contraste 4.9:1 em branco */

  /* === CORES SEMÂNTICAS === */
  --nix-success: #065f46;          /* Verde escuro - Contraste 7.8:1 */
  --nix-success-light: #10b981;    /* Verde médio - Contraste 4.7:1 */
  --nix-danger: #991b1b;           /* Vermelho escuro - Contraste 8.1:1 */
  --nix-danger-light: #ef4444;     /* Vermelho médio - Contraste 4.8:1 */
  --nix-warning: #92400e;          /* Amarelo escuro - Contraste 6.2:1 */
  --nix-warning-light: #f59e0b;    /* Amarelo médio - Contraste 4.6:1 */
  --nix-info: #1e40af;            /* Azul info - Contraste 7.3:1 */
  --nix-info-light: #3b82f6;      /* Azul info claro - Contraste 4.9:1 */

  /* === TEMA CLARO (PADRÃO) === */
  --bg-color: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --text-color: #0f172a;           /* Contraste 16.8:1 */
  --text-muted: #475569;           /* Contraste 7.2:1 */
  --text-light: #64748b;           /* Contraste 5.8:1 */
  --border-color: #cbd5e1;         /* Contraste 3.2:1 */
  --border-light: #e2e8f0;        /* Contraste 2.1:1 */

  /* === LINKS === */
  --link-color: #5b21b6;           /* Roxo escuro - Contraste 6.8:1 */
  --link-hover-color: #7c3aed;     /* Roxo elegante - Contraste 5.1:1 */
  --link-visited-color: #6366f1;   /* Índigo - Contraste 4.9:1 */

  /* === ESTADOS INTERATIVOS === */
  --focus-ring: #7c3aed;           /* Roxo elegante para focus */
  --focus-ring-offset: #ffffff;
  --hover-bg: #f3f4f6;            /* Cinza suave para hover */
  --active-bg: #e5e7eb;           /* Cinza médio para active */
  --disabled-bg: #f8fafc;
  --disabled-text: #94a3b8;       /* Contraste 4.6:1 */

  /* === TIPOGRAFIA === */
  --font-family-sans-serif: 'Inter', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-family-serif: 'Merriweather', 'Georgia', 'Times New Roman', serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;

  /* === LAYOUT === */
  --border-radius: 0.5rem;
  --border-radius-sm: 0.25rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;

  /* === SOMBRAS === */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* === TRANSIÇÕES === */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  /* === COMPATIBILIDADE (DEPRECATED - SERÁ REMOVIDO) === */
  --primary-color: var(--nix-primary);
  --primary-light: var(--nix-primary-light);
  --primary-dark: var(--nix-primary-dark);
  --secondary-color: var(--nix-secondary);
  --success-color: var(--nix-success);
  --danger-color: var(--nix-danger);
  --warning-color: var(--nix-warning);
  --info-color: var(--nix-info);
  --box-shadow: var(--shadow);
  --box-shadow-lg: var(--shadow-lg);
  --transition: var(--transition-normal);
  --navbar-height: 60px;
  --footer-height: auto;
}

/* === TEMA ESCURO - ACESSIBILIDADE OTIMIZADA === */
[data-theme="dark"] {
    /* === BACKGROUNDS === */
    --bg-color: #0f172a;            /* Azul muito escuro */
    --bg-secondary: #1e293b;        /* Azul escuro médio */
    --bg-tertiary: #334155;         /* Azul médio */

    /* === TEXTOS === */
    --text-color: #ffffff;          /* Branco puro - Contraste máximo */
    --text-muted: #e2e8f0;          /* Cinza muito claro - Contraste 8.5:1 */
    --text-light: #cbd5e1;          /* Cinza claro - Contraste 7.2:1 */

    /* === BORDAS === */
    --border-color: #475569;        /* Cinza escuro - Contraste 4.2:1 */
    --border-light: #334155;        /* Cinza mais escuro - Contraste 2.8:1 */

    /* === LINKS === */
    --link-color: #a855f7;          /* Roxo claro - Contraste 4.5:1 */
    --link-hover-color: #c084fc;    /* Roxo mais claro - Contraste 4.2:1 */
    --link-visited-color: #818cf8;  /* Índigo claro - Contraste 4.3:1 */

    /* === ESTADOS INTERATIVOS === */
    --focus-ring: #a855f7;          /* Roxo claro para focus */
    --focus-ring-offset: #0f172a;
    --hover-bg: #1e293b;
    --active-bg: #334155;
    --disabled-bg: #1e293b;
    --disabled-text: #64748b;       /* Contraste 4.5:1 */

    /* === CORES SEMÂNTICAS AJUSTADAS === */
    --nix-success: #22c55e;         /* Verde claro - Contraste 4.7:1 */
    --nix-success-light: #4ade80;   /* Verde mais claro - Contraste 4.2:1 */
    --nix-danger: #ef4444;          /* Vermelho claro - Contraste 4.8:1 */
    --nix-danger-light: #f87171;    /* Vermelho mais claro - Contraste 4.3:1 */
    --nix-warning: #f59e0b;         /* Amarelo claro - Contraste 4.6:1 */
    --nix-warning-light: #fbbf24;   /* Amarelo mais claro - Contraste 4.2:1 */
    --nix-info: #60a5fa;           /* Azul info claro - Contraste 5.1:1 */
    --nix-info-light: #93c5fd;     /* Azul info mais claro - Contraste 4.6:1 */

    /* === CORES DE DESTAQUE AJUSTADAS === */
    --nix-accent: #a855f7;          /* Roxo claro - Contraste 4.5:1 */
    --nix-accent-light: #c084fc;    /* Roxo mais claro - Contraste 4.2:1 */
    --nix-accent-dark: #7c3aed;     /* Roxo escuro - Contraste 5.1:1 */
    --nix-accent-alt: #818cf8;      /* Índigo claro - Contraste 4.3:1 */

    /* === COMPATIBILIDADE BOOTSTRAP === */
    --bs-body-bg: var(--bg-color);
    --bs-body-color: var(--text-color);
    --bs-border-color: var(--border-color);
    --bs-secondary-bg: var(--bg-secondary);
    --bs-tertiary-bg: var(--bg-tertiary);

    /* === COMPONENTES ESPECÍFICOS === */
    --navbar-bg: var(--bg-color);
    --footer-bg: var(--bg-secondary);
    --card-bg: var(--bg-secondary);
}

/* === TEMA ESCURO - ESTILOS DE COMPONENTES === */

/* Body */
[data-theme="dark"] body {
    background-color: var(--bg-color);
    color: var(--text-color);
}

/* Navbar */
[data-theme="dark"] .navbar {
    background: linear-gradient(135deg, var(--bg-color) 0%, var(--bg-secondary) 100%) !important;
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-md);
}

[data-theme="dark"] .navbar-brand {
    color: var(--text-color) !important;
}

[data-theme="dark"] .navbar-nav .nav-link {
    color: var(--text-color) !important;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    text-decoration: none;
}

/* Alinhamento de ícones no tema escuro */
[data-theme="dark"] .navbar-nav .nav-link i {
    width: 16px;
    height: 16px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.5rem;
    font-size: 14px;
    line-height: 1;
    flex-shrink: 0;
}

[data-theme="dark"] .navbar-nav .nav-link:hover {
    color: var(--text-color) !important;
    background-color: var(--hover-bg);
}

[data-theme="dark"] .navbar-nav .nav-link.active {
    color: var(--text-color) !important;
    background-color: var(--nix-accent);
    font-weight: 600;
}

/* Cards */
[data-theme="dark"] .card {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-color);
    box-shadow: var(--shadow-md);
}

[data-theme="dark"] .card-header {
    background-color: var(--bg-tertiary);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
    border-color: var(--nix-accent);
}

/* Text utilities */
[data-theme="dark"] .text-muted {
    color: var(--text-light) !important;
}

[data-theme="dark"] .text-dark {
    color: var(--text-color) !important;
}

[data-theme="dark"] .text-body {
    color: var(--text-color) !important;
}

[data-theme="dark"] .text-secondary {
    color: var(--text-light) !important;
}

[data-theme="dark"] .bg-light {
    background-color: var(--bg-secondary) !important;
}

/* Forms */
[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-color);
    box-shadow: var(--shadow-sm);
}

[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus {
    background-color: var(--bg-secondary);
    border-color: var(--nix-accent);
    color: var(--text-color);
    box-shadow: 0 0 0 0.2rem rgba(124, 58, 237, 0.25);
    outline: 2px solid transparent;
    outline-offset: 2px;
}

/* Pesquisa na navbar - tema escuro */
[data-theme="dark"] .navbar .form-control {
    border-color: var(--border-color);
    background-color: var(--bg-secondary);
    color: var(--text-color);
}

[data-theme="dark"] .navbar .form-control::placeholder {
    color: var(--text-light);
}

[data-theme="dark"] .navbar .form-control:focus {
    border-color: var(--nix-accent);
    background-color: var(--bg-secondary);
    color: var(--text-color);
    box-shadow: 0 0 0 0.2rem rgba(124, 58, 237, 0.25);
}

[data-theme="dark"] .navbar .btn-outline-light {
    border-color: var(--border-color);
    background-color: var(--bg-secondary);
    color: var(--text-color);
}

[data-theme="dark"] .navbar .btn-outline-light:hover,
[data-theme="dark"] .navbar .btn-outline-light:focus {
    background-color: var(--nix-accent);
    border-color: var(--nix-accent);
    color: white;
}

[data-theme="dark"] .form-control::placeholder {
    color: var(--text-light);
    opacity: 1;
}

/* Buttons */
[data-theme="dark"] .btn-outline-secondary {
    color: var(--text-color);
    border-color: var(--border-color);
    background-color: transparent;
}

[data-theme="dark"] .btn-outline-secondary:hover {
    background-color: var(--hover-bg);
    border-color: var(--text-color);
    color: var(--text-color);
    box-shadow: var(--shadow);
}

[data-theme="dark"] .btn-primary {
    background-color: var(--nix-accent);
    border-color: var(--nix-accent);
    color: var(--bg-color);
    font-weight: 500;
    box-shadow: var(--shadow);
}

[data-theme="dark"] .btn-primary:hover {
    background-color: var(--nix-accent-light);
    border-color: var(--nix-accent-light);
    color: var(--bg-color);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

[data-theme="dark"] .btn:focus {
    outline: 2px solid var(--focus-ring);
    outline-offset: 2px;
}

/* TinyMCE Editor - Tema Escuro */
[data-theme="dark"] .tox .tox-editor-header {
    background-color: var(--bg-secondary) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .tox .tox-toolbar {
    background-color: var(--bg-secondary) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .tox .tox-toolbar__group {
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .tox .tox-tbtn {
    color: var(--text-color) !important;
    background-color: transparent !important;
}

[data-theme="dark"] .tox .tox-tbtn:hover {
    background-color: var(--hover-bg) !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .tox .tox-tbtn--enabled {
    background-color: var(--nix-accent) !important;
    color: white !important;
}

[data-theme="dark"] .tox .tox-edit-area {
    background-color: var(--bg-color) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .tox .tox-edit-area iframe {
    background-color: var(--bg-color) !important;
}

[data-theme="dark"] .tox .tox-statusbar {
    background-color: var(--bg-secondary) !important;
    border-color: var(--border-color) !important;
    color: var(--text-muted) !important;
}

[data-theme="dark"] .tox .tox-menubar {
    background-color: var(--bg-secondary) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .tox .tox-mbtn {
    color: var(--text-color) !important;
}

[data-theme="dark"] .tox .tox-mbtn:hover {
    background-color: var(--hover-bg) !important;
    color: var(--text-color) !important;
}

/* TinyMCE Dropdown Menus */
[data-theme="dark"] .tox .tox-menu {
    background-color: var(--bg-secondary) !important;
    border-color: var(--border-color) !important;
    box-shadow: var(--shadow-lg) !important;
}

[data-theme="dark"] .tox .tox-collection__item {
    color: var(--text-color) !important;
    background-color: transparent !important;
}

[data-theme="dark"] .tox .tox-collection__item:hover {
    background-color: var(--hover-bg) !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .tox .tox-collection__item--enabled {
    background-color: var(--nix-accent) !important;
    color: white !important;
}

/* TinyMCE Content Area */
[data-theme="dark"] .tox-tinymce {
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .mce-content-body {
    background-color: var(--bg-color) !important;
    color: var(--text-color) !important;
    font-family: var(--font-family-sans-serif) !important;
}

[data-theme="dark"] .mce-content-body h1,
[data-theme="dark"] .mce-content-body h2,
[data-theme="dark"] .mce-content-body h3,
[data-theme="dark"] .mce-content-body h4,
[data-theme="dark"] .mce-content-body h5,
[data-theme="dark"] .mce-content-body h6 {
    color: var(--text-color) !important;
}

[data-theme="dark"] .mce-content-body p {
    color: var(--text-color) !important;
}

[data-theme="dark"] .mce-content-body a {
    color: var(--nix-accent-light) !important;
}

[data-theme="dark"] .mce-content-body blockquote {
    border-left-color: var(--nix-accent) !important;
    background-color: var(--bg-secondary) !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .mce-content-body code {
    background-color: var(--bg-secondary) !important;
    color: var(--nix-accent-light) !important;
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .mce-content-body pre {
    background-color: var(--bg-secondary) !important;
    color: var(--text-color) !important;
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .mce-content-body table {
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .mce-content-body td,
[data-theme="dark"] .mce-content-body th {
    border-color: var(--border-color) !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .mce-content-body th {
    background-color: var(--bg-secondary) !important;
}

/* Dropdown Menu - Tema Escuro */
[data-theme="dark"] .dropdown-menu {
    background-color: var(--bg-secondary) !important;
    border-color: var(--border-color) !important;
    box-shadow: var(--shadow-lg) !important;
}

[data-theme="dark"] .dropdown-item {
    color: var(--text-color) !important;
    background-color: transparent !important;
}

[data-theme="dark"] .dropdown-item:hover,
[data-theme="dark"] .dropdown-item:focus {
    background-color: var(--hover-bg) !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .dropdown-item.active {
    background-color: var(--nix-accent) !important;
    color: white !important;
}

[data-theme="dark"] .dropdown-header {
    color: var(--text-color) !important;
}

[data-theme="dark"] .dropdown-divider {
    border-color: var(--border-color) !important;
}

/* Breadcrumbs - Tema Escuro */
[data-theme="dark"] .breadcrumb {
    background-color: var(--bg-secondary) !important;
}

[data-theme="dark"] .breadcrumb-item {
    color: var(--text-color) !important;
}

[data-theme="dark"] .breadcrumb-item.active {
    color: var(--text-light) !important;
}

[data-theme="dark"] .breadcrumb-item + .breadcrumb-item::before {
    color: var(--text-light) !important;
}

/* Alert - Tema Escuro */
[data-theme="dark"] .alert {
    background-color: var(--bg-secondary) !important;
    border-color: var(--border-color) !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .alert-info {
    background-color: rgba(124, 58, 237, 0.1) !important;
    border-color: var(--nix-accent) !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .alert-success {
    background-color: rgba(34, 197, 94, 0.1) !important;
    border-color: #22c55e !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .alert-warning {
    background-color: rgba(245, 158, 11, 0.1) !important;
    border-color: #f59e0b !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .alert-danger {
    background-color: rgba(239, 68, 68, 0.1) !important;
    border-color: #ef4444 !important;
    color: var(--text-color) !important;
}

/* === ESTILOS BASE === */
body {
    font-family: var(--font-family-sans-serif);
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--bg-color);
    transition: var(--transition-normal);
    margin: 0;
    padding: 0;
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Layout fixes */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding-left: 15px;
    padding-right: 15px;
}

@media (min-width: 576px) {
    .container {
        max-width: 540px;
    }
}

@media (min-width: 768px) {
    .container {
        max-width: 720px;
    }
}

@media (min-width: 992px) {
    .container {
        max-width: 960px;
    }
}

@media (min-width: 1200px) {
    .container {
        max-width: 1140px;
    }
}

/* Typography (Django Style Guide) */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-sans-serif);
    font-weight: 500;
    line-height: 1.3;
    color: var(--text-color);
    margin-bottom: 0.75rem;
}

/* Django Typography Hierarchy */
h1 {
    font-size: 2.25rem; /* 36px */
    font-weight: 600;
    margin-bottom: 1rem;
}

h2 {
    font-size: 1.75rem; /* 28px */
    font-weight: 500;
    margin-bottom: 0.875rem;
}

h3 {
    font-size: 1.375rem; /* 22px */
    font-weight: 500;
    margin-bottom: 0.75rem;
}

h4 {
    font-size: 1.125rem; /* 18px */
    font-weight: 500;
    margin-bottom: 0.625rem;
}

h5 {
    font-size: 1rem; /* 16px */
    font-weight: 500;
    margin-bottom: 0.5rem;
}

h6 {
    font-size: 0.875rem; /* 14px */
    font-weight: 500;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Paragraph styles */
p {
    font-family: var(--font-family-sans-serif);
    line-height: 1.6;
    margin-bottom: 1rem;
    color: var(--text-color);
}

/* Text utilities */
.text-sans {
    font-family: var(--font-family-sans-serif) !important;
}

.text-serif {
    font-family: var(--font-family-serif) !important;
}

/* Code and monospace */
code, pre, .monospace {
    font-family: var(--font-family-mono);
    background-color: rgba(0, 0, 0, 0.05);
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-size: 0.875em;
}

pre {
    padding: 1rem;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: var(--border-radius);
    overflow-x: auto;
}

.lead {
    font-weight: 300;
    font-size: 1.25rem;
    font-family: var(--font-family-sans-serif);
    line-height: 1.5;
    margin-bottom: 1.5rem;
}

/* === LINKS === */
a {
    color: var(--link-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--link-hover-color);
    text-decoration: underline;
}

a:visited {
    color: var(--link-visited-color);
}

a:focus {
    outline: 2px solid var(--focus-ring);
    outline-offset: 2px;
    border-radius: var(--border-radius-sm);
}

/* === BOTÕES === */
.btn {
    font-family: var(--font-family-sans-serif);
    font-weight: 500;
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
    border: 1px solid transparent;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow);
    text-decoration: none;
}

.btn:focus {
    outline: 2px solid var(--focus-ring);
    outline-offset: 2px;
}

.btn-primary {
    background-color: var(--nix-accent);
    border-color: var(--nix-accent);
    color: white;
    font-weight: 500;
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    background-color: var(--nix-accent-dark);
    border-color: var(--nix-accent-dark);
    color: white;
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.btn-outline-primary {
    color: var(--nix-primary);
    border-color: var(--nix-primary);
    background-color: transparent;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-outline-primary:hover {
    background-color: var(--nix-primary);
    border-color: var(--nix-primary);
    color: white;
    box-shadow: 0 2px 8px rgba(124, 58, 237, 0.3);
    transform: translateY(-1px);
}

/* Call to action button (Django style) */
.btn-cta {
    background-color: var(--nix-primary);
    border-color: var(--nix-primary);
    color: white;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
}

.btn-cta:hover {
    background-color: var(--nix-primary);
    border-color: var(--nix-primary);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--box-shadow-lg);
}

.btn-floating {
    border-radius: 50%;
    width: 56px;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Cards */
.card {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: var(--transition);
    background-color: var(--bg-color);
    overflow: hidden;
}

.card:hover {
    border: 2px solid var(--nix-accent-alt) !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12), 0 0 0 0.15rem rgba(68, 183, 139, 0.25);
    transition: all 0.2s ease;
    transform: translateY(-2px);
}

.card-header {
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
    padding: 0.75rem 1rem;
    font-weight: 500;
    color: var(--text-color);
}

.card-body {
    padding: 1.25rem;
}

.card-title {
    font-size: 1.125rem;
    font-weight: 500;
    margin-bottom: 0.75rem;
    line-height: 1.3;
}

.card-text {
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.card-img-top {
    border-radius: 0;
    transition: var(--transition);
}

.card:hover .card-img-top {
    transform: scale(1.02);
}

/* Image Optimization Styles */
.img-optimized {
    max-width: 100%;
    height: auto;
    object-fit: cover;
    transition: var(--transition);
}

.img-lazy {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.img-lazy.loaded {
    opacity: 1;
}

.img-placeholder {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

[data-theme="dark"] .img-placeholder {
    background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
    background-size: 200% 100%;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Responsive image containers */
.img-container {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius);
}

.img-container-16-9 {
    aspect-ratio: 16/9;
}

.img-container-4-3 {
    aspect-ratio: 4/3;
}

.img-container-1-1 {
    aspect-ratio: 1/1;
}

.img-container img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Image overlay effects */
.img-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.7) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.img-container:hover .img-overlay {
    opacity: 1;
}

/* WebP support detection */
.webp .img-webp {
    display: block;
}

.no-webp .img-webp {
    display: none;
}

.webp .img-fallback {
    display: none;
}

.no-webp .img-fallback {
    display: block;
}

/* Navigation */
.navbar {
    padding: 0.5rem 0;
    background: linear-gradient(135deg, var(--nix-primary) 0%, var(--nix-primary-dark) 100%);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1020;
}

.navbar-django {
    background: linear-gradient(135deg, var(--nix-primary) 0%, var(--nix-primary-dark) 100%);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: white !important;
    text-decoration: none;
    display: flex;
    align-items: center;
}

.navbar-brand:hover {
    color: rgba(255, 255, 255, 0.9) !important;
    text-decoration: none;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: var(--transition);
    border-radius: var(--border-radius);
    margin: 0 0.25rem;
    color: rgba(255, 255, 255, 0.9) !important;
    padding: 0.5rem 0.75rem;
    display: flex;
    align-items: center;
    text-decoration: none;
}

/* Alinhamento perfeito de ícones na navbar */
.navbar-nav .nav-link i {
    width: 16px;
    height: 16px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.5rem;
    font-size: 14px;
    line-height: 1;
    flex-shrink: 0;
}

/* Alinhamento de ícones no dropdown */
.dropdown-item {
    display: flex;
    align-items: center;
    text-decoration: none;
}

.dropdown-item i {
    width: 16px;
    height: 16px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.5rem;
    font-size: 14px;
    line-height: 1;
    flex-shrink: 0;
}

/* Alinhamento do header do dropdown */
.dropdown-header {
    display: flex;
    align-items: center;
}

.dropdown-header i {
    width: 16px;
    height: 16px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.5rem;
    font-size: 14px;
    line-height: 1;
    flex-shrink: 0;
}

/* Alinhamento do botão de busca */
.navbar .btn {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 38px;
}

.navbar .btn i {
    font-size: 14px;
    line-height: 1;
}

/* Alinhamento do brand */
.navbar-brand {
    display: flex;
    align-items: center;
}

.navbar-brand img {
    margin-right: 0.5rem;
}

/* Alinhamento da pesquisa na navbar */
.navbar .form-django {
    display: flex;
    align-items: center;
    margin: 0;
}

.navbar .input-group {
    display: flex;
    align-items: center;
    width: 280px;
    max-width: 100%;
}

.navbar .form-control {
    border-color: rgba(255, 255, 255, 0.3);
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    border-radius: 0.375rem 0 0 0.375rem;
    height: 38px;
}

.navbar .form-control::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.navbar .form-control:focus {
    border-color: var(--nix-accent);
    background-color: rgba(255, 255, 255, 0.15);
    color: white;
    box-shadow: 0 0 0 0.2rem rgba(124, 58, 237, 0.25);
}

.navbar .btn-outline-light {
    border-color: rgba(255, 255, 255, 0.3);
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    border-radius: 0 0.375rem 0.375rem 0;
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 38px;
}

.navbar .btn-outline-light:hover,
.navbar .btn-outline-light:focus {
    background-color: var(--nix-accent);
    border-color: var(--nix-accent);
    color: white;
}

/* Alinhamento do dropdown do usuário */
.navbar-nav .dropdown-toggle {
    display: flex;
    align-items: center;
    text-decoration: none;
}

.avatar-sm {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

/* Melhorar alinhamento do toggle de tema */
.theme-toggle .btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    padding: 0;
}

.theme-toggle .btn i {
    font-size: 16px;
    line-height: 1;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white !important;
}

.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    font-weight: 600;
    color: white !important;
}

.navbar-toggler {
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 0.25rem 0.5rem;
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.9%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Dropdown */
.dropdown-menu {
    border: none;
    box-shadow: var(--box-shadow-lg);
    border-radius: var(--border-radius);
}

.dropdown-item {
    transition: var(--transition);
    border-radius: calc(var(--border-radius) - 2px);
    margin: 0.125rem 0.5rem;
}

.dropdown-item:hover {
    background-color: var(--nix-secondary);
}

/* Forms */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    background-color: var(--bg-color);
    color: var(--text-color);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
}

.form-control:focus, .form-select:focus {
    border-color: var(--nix-accent);
    box-shadow: 0 0 0 0.2rem rgba(124, 58, 237, 0.25), inset 0 1px 2px rgba(0, 0, 0, 0.05);
    outline: none;
    background-color: var(--bg-color);
}

/* Badges */
.badge {
    font-weight: 500;
    border-radius: calc(var(--border-radius) * 2);
}

/* Alerts */
.alert {
    border: none;
    border-radius: var(--border-radius);
    border-left: 4px solid;
}

.alert-primary {
    border-left-color: var(--nix-primary);
}

.alert-success {
    border-left-color: var(--nix-accent-alt);
}

.alert-danger {
    border-left-color: var(--nix-danger-color);
}

.alert-warning {
    border-left-color: var(--nix-warning-color);
}

.alert-info {
    border-left-color: var(--nix-info-color);
}

/* Pagination */
.pagination .page-link {
    border-radius: var(--border-radius);
    margin: 0 0.125rem;
    border: 1px solid #dee2e6;
    transition: var(--transition);
}

.pagination .page-link:hover {
    transform: translateY(-1px);
    box-shadow: var(--box-shadow);
}

/* Breadcrumb */
.breadcrumb {
    background-color: transparent;
    padding: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    font-weight: 600;
    color: var(--nix-secondary-color);
}

/* Footer */
footer {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: var(--nix-text-muted);
    border-top: 1px solid var(--nix-border);
}

footer a {
    color: var(--nix-text-muted);
}

footer a:hover {
    color: var(--nix-primary-color) !important;
}

/* Dark theme footer */
[data-theme="dark"] footer,
[data-theme="dark"] .footer-django {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
    color: #e2e8f0 !important;
    border-top: 1px solid var(--nix-border);
}

[data-theme="dark"] footer a,
[data-theme="dark"] .footer-django a {
    color: #e2e8f0 !important;
    opacity: 0.9;
}

[data-theme="dark"] footer a:hover,
[data-theme="dark"] .footer-django a:hover {
    color: var(--nix-primary-light) !important;
    opacity: 1;
}

[data-theme="dark"] footer p,
[data-theme="dark"] footer span,
[data-theme="dark"] footer div,
[data-theme="dark"] .footer-django p,
[data-theme="dark"] .footer-django span,
[data-theme="dark"] .footer-django div {
    color: #e2e8f0 !important;
}

/* Corrigir classes específicas do footer */
[data-theme="dark"] .text-theme-light {
    color: #e2e8f0 !important;
}

[data-theme="dark"] .text-muted {
    color: #cbd5e0 !important;
}

/* Utilities */
.shadow-sm {
    box-shadow: var(--box-shadow) !important;
}

.shadow {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.shadow-lg {
    box-shadow: var(--box-shadow-lg) !important;
}

/* Avatar */
.avatar-sm {
    width: 32px;
    height: 32px;
}

.avatar-md {
    width: 48px;
    height: 48px;
}

.avatar-lg {
    width: 64px;
    height: 64px;
}

/* Loading */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInDown {
    from { opacity: 0; transform: translateY(-30px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInLeft {
    from { opacity: 0; transform: translateX(-30px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes fadeInRight {
    from { opacity: 0; transform: translateX(30px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes scaleIn {
    from { opacity: 0; transform: scale(0.9); }
    to { opacity: 1; transform: scale(1); }
}

@keyframes slideInUp {
    from { transform: translateY(100%); }
    to { transform: translateY(0); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
    40%, 43% { transform: translateY(-10px); }
    70% { transform: translateY(-5px); }
    90% { transform: translateY(-2px); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes glow {
    0%, 100% { box-shadow: 0 0 5px rgba(124, 58, 237, 0.5); }
    50% { box-shadow: 0 0 20px rgba(124, 58, 237, 0.8), 0 0 30px rgba(124, 58, 237, 0.6); }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.fade-in-down {
    animation: fadeInDown 0.6s ease-out;
}

.fade-in-left {
    animation: fadeInLeft 0.6s ease-out;
}

.fade-in-right {
    animation: fadeInRight 0.6s ease-out;
}

.scale-in {
    animation: scaleIn 0.4s ease-out;
}

.slide-in-up {
    animation: slideInUp 0.5s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

.bounce {
    animation: bounce 1s;
}

.shake {
    animation: shake 0.5s;
}

.spin {
    animation: spin 1s linear infinite;
}

.glow {
    animation: glow 2s ease-in-out infinite;
}

/* Staggered animations */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }

/* Hover animations */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate {
    transition: transform 0.3s ease;
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

.hover-glow {
    transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(124, 58, 237, 0.6);
}

/* Loading animations */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

.loading-dots {
    display: inline-block;
}

.loading-dots::after {
    content: '';
    animation: dots 1.5s steps(4, end) infinite;
}

@keyframes dots {
    0%, 20% { content: ''; }
    40% { content: '.'; }
    60% { content: '..'; }
    80%, 100% { content: '...'; }
}

/* Scroll animations */
.scroll-reveal {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* Page transition animations */
.page-enter {
    animation: fadeInUp 0.5s ease-out;
}

.page-exit {
    animation: fadeOut 0.3s ease-in;
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

/* Micro-interactions */
.btn {
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn:active::before {
    width: 300px;
    height: 300px;
}

/* Form animations */
.form-control,
.form-select {
    transition: border-color 0.3s ease, box-shadow 0.3s ease, transform 0.2s ease;
}

.form-control:focus,
.form-select:focus {
    transform: translateY(-1px);
}

/* Card animations */
.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

/* Navbar animations */
.navbar {
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.nav-link {
    position: relative;
    transition: color 0.3s ease;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background-color: rgba(255, 255, 255, 0.8);
    transition: width 0.3s ease, left 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
    left: 0;
}

/* Layout improvements */
.container {
    padding-left: 1rem;
    padding-right: 1rem;
}

/* Spacing utilities */
.my-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
}

.mb-4 {
    margin-bottom: 1.5rem !important;
}

.mt-4 {
    margin-top: 1.5rem !important;
}

/* Grid improvements */
.row {
    margin-left: -0.75rem;
    margin-right: -0.75rem;
}

.row > * {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}

.g-4 > * {
    padding: 0.75rem;
}

/* Badge improvements */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
}

/* Button improvements */
.btn {
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.125rem;
}

/* Responsive - Large Tablets */
@media (max-width: 1199.98px) {
    .container {
        max-width: 100%;
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

/* Responsive - Tablets */
@media (max-width: 991.98px) {
    .navbar .input-group {
        width: 240px;
    }

    .navbar .form-control {
        font-size: 14px;
    }

    /* Melhorar espaçamento em tablets */
    .container {
        padding-left: 0.875rem;
        padding-right: 0.875rem;
    }

    /* Cards responsivos */
    .card {
        margin-bottom: 1rem;
    }

    /* Navbar responsiva */
    .navbar-nav .nav-link {
        padding: 0.625rem 0.875rem;
    }
}

/* Responsive - Mobile */
@media (max-width: 768px) {
    .container {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    .navbar-brand {
        font-size: 1.25rem;
    }

    /* Alinhamento responsivo da navbar */
    .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
        justify-content: flex-start;
        min-height: 44px;
        display: flex;
        align-items: center;
    }

    .navbar-nav .nav-link i {
        margin-right: 0.75rem;
        font-size: 16px;
        width: 20px;
        height: 20px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    .dropdown-item {
        padding: 0.75rem 1rem;
        min-height: 44px;
        display: flex;
        align-items: center;
    }

    .dropdown-item i {
        margin-right: 0.75rem;
        font-size: 16px;
        width: 20px;
        height: 20px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    /* Pesquisa responsiva */
    .navbar .form-django {
        width: 100%;
        margin: 1rem 0;
        order: 3;
    }

    .navbar .input-group {
        width: 100%;
        max-width: none;
    }

    .navbar .form-control,
    .navbar .btn-outline-light {
        height: 44px;
        font-size: 16px;
    }

    .navbar .form-control {
        padding: 0.75rem 1rem;
    }

    .navbar .btn-outline-light {
        min-width: 44px;
        padding: 0.75rem;
    }

    /* Botões touch-friendly */
    .btn {
        min-height: 44px;
        padding: 0.75rem 1rem;
    }

    .btn-sm {
        min-height: 38px;
        padding: 0.5rem 0.75rem;
    }

    .btn-floating {
        width: 48px;
        height: 48px;
    }

    /* Cards responsivos */
    .card {
        margin-bottom: 1rem;
    }

    .card:hover {
        transform: none;
    }

    /* Typography responsiva */
    h1, .h1 {
        font-size: 1.875rem;
    }

    h2, .h2 {
        font-size: 1.5rem;
    }

    h3, .h3 {
        font-size: 1.25rem;
    }

    /* Espaçamentos responsivos */
    .my-5 {
        margin-top: 2rem !important;
        margin-bottom: 2rem !important;
    }

    .py-5 {
        padding-top: 2rem !important;
        padding-bottom: 2rem !important;
    }

    /* Grid responsivo */
    .row {
        margin-left: -0.5rem;
        margin-right: -0.5rem;
    }

    .row > * {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }

    /* Formulários responsivos */
    .form-control,
    .form-select {
        min-height: 44px;
        font-size: 16px;
        padding: 0.75rem 1rem;
    }

    /* Dropdown responsivo */
    .dropdown-menu {
        min-width: 200px;
        max-width: 90vw;
    }
}

/* Responsive - Small Mobile */
@media (max-width: 576px) {
    .container {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }

    /* Typography extra small */
    .display-1, .display-2, .display-3, .display-4, .display-5, .display-6 {
        font-size: 1.5rem !important;
    }

    .h1, h1 {
        font-size: 1.5rem !important;
    }

    .h2, h2 {
        font-size: 1.25rem !important;
    }

    .h3, h3 {
        font-size: 1.125rem !important;
    }

    .h4, .h5, .h6, h4, h5, h6 {
        font-size: 1rem !important;
    }

    /* Cards extra small */
    .card {
        padding: 0.75rem !important;
        margin-bottom: 0.75rem;
    }

    .card-body {
        padding: 0.75rem;
    }

    /* Navbar extra small */
    .navbar-brand {
        font-size: 1.125rem;
    }

    .navbar-nav .nav-link {
        padding: 0.625rem 0.75rem;
        font-size: 0.875rem;
    }

    /* Botões extra small */
    .btn {
        font-size: 0.875rem;
        padding: 0.625rem 0.875rem;
    }

    /* Grid extra small */
    .row {
        margin-left: -0.25rem;
        margin-right: -0.25rem;
    }

    .row > * {
        padding-left: 0.25rem;
        padding-right: 0.25rem;
    }

    /* Espaçamentos extra small */
    .my-5 {
        margin-top: 1.5rem !important;
        margin-bottom: 1.5rem !important;
    }

    .py-5 {
        padding-top: 1.5rem !important;
        padding-bottom: 1.5rem !important;
    }

    /* Formulários extra small */
    .form-control,
    .form-select {
        font-size: 16px; /* Evita zoom no iOS */
        padding: 0.625rem 0.875rem;
    }
}

/* Print */
@media print {
    .navbar, .breadcrumb, footer, .btn-floating {
        display: none !important;
    }
    
    .container {
        max-width: none !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --nix-secondary: #343a40;
        --nix-dark: #f8f9fa;
    }
}

/* Focus styles for accessibility */
.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: 2px solid var(--nix-accent);
    outline-offset: 2px;
}

/* Skip link */
.visually-hidden-focusable:focus {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9999;
    padding: 0.5rem 1rem;
    background-color: var(--nix-primary);
    color: white;
    text-decoration: none;
    border-radius: 0 0 var(--border-radius) 0;
}

/* Theme Toggle Styles */
.theme-toggle {
    display: flex;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 1.5rem;
    padding: 0.25rem;
    gap: 0.25rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.theme-option {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    padding: 0.5rem;
    border-radius: 1.25rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    font-size: 0.875rem;
}

.theme-option:hover {
    color: rgba(255, 255, 255, 0.9);
    background-color: rgba(255, 255, 255, 0.1);
    transform: scale(1.05);
}

.theme-option.active {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.theme-option:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 2px;
}

/* Dark theme toggle adjustments */
[data-theme="dark"] .theme-toggle {
    background-color: rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .theme-option {
    color: rgba(255, 255, 255, 0.6);
}

[data-theme="dark"] .theme-option:hover {
    color: rgba(255, 255, 255, 0.9);
    background-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .theme-option.active {
    background-color: var(--nix-primary);
    color: white;
}

#btn-back-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: none;
    z-index: 1000;
    border: none;
    outline: none;
    cursor: pointer;
    padding: 15px;
    border-radius: 50%;
    font-size: 16px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
}

#btn-back-to-top:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.4);
}

/* --- Article List Styles (migrados de article-list.css) --- */
.articles-list {
    max-width: 100%;
}
.article-item {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 2rem;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}
.article-item:hover {
    background-color: var(--bg-secondary);
    border-radius: 0.5rem;
    padding: 1rem;
    margin: 0 -1rem 2rem -1rem;
}
.article-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}
.article-header {
    margin-bottom: 1.5rem;
}
.article-title {
    font-size: 1.75rem;
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}
.article-title a {
    text-decoration: none;
    color: inherit;
    transition: color 0.3s ease;
}
.article-title a:hover {
    color: var(--primary-light) !important;
    text-decoration: none;
}
.featured-image {
    position: relative;
    overflow: hidden;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
    width: 100%;
    height: 300px;
    background-color: var(--bg-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}
.featured-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, var(--bg-secondary) 25%, transparent 25%),
                linear-gradient(-45deg, var(--bg-secondary) 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, var(--bg-secondary) 75%),
                linear-gradient(-45deg, transparent 75%, var(--bg-secondary) 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    opacity: 0.3;
}
.article-meta a:hover {
    color: var(--primary-light) !important;
    text-decoration: none;
}
.article-excerpt {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 1rem;
    color: var(--text-color);
}
.article-tags .badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
    margin-right: 0.5rem;
    margin-bottom: 0.25rem;
    border-radius: 0.375rem;
}
.article-tags .badge a {
    color: white;
    text-decoration: none;
    transition: opacity 0.3s ease;
}
.article-tags .badge a:hover {
    opacity: 0.8;
    color: white;
}
.article-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}
.reading-info {
    font-size: 0.85rem;
    color: var(--text-muted);
    display: flex;
    align-items: center;
}
.reading-info i {
    margin-right: 0.25rem;
    opacity: 0.7;
}
.admin-actions {
    background-color: var(--bg-secondary);
    border-radius: 0.375rem;
    padding: 0.75rem;
    margin-top: 1rem;
    border-top: 1px solid #e9ecef;
}
.admin-actions .btn {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}
@media (max-width: 768px) {
    .article-title {
        font-size: 1.5rem;
    }
    .article-footer {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    .article-item:hover {
        padding: 0.5rem;
        margin: 0 -0.5rem 2rem -0.5rem;
    }
}

/* --- Config Admin Styles (migrados de config-admin.css) --- */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    background-color: var(--bg-secondary); /* Sólido no claro */
    border-right: 1px solid var(--border-color);
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.08);
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    overflow-y: auto;
    overflow-x: hidden;
    width: 260px;
    transition: background 0.3s, box-shadow 0.3s;
}
@media (max-width: 991.98px) {
    .sidebar {
        position: fixed !important;
        left: 0;
        top: 0;
        width: 80vw !important;
        max-width: 320px;
        height: 100vh !important;
        background-color: var(--bg-secondary) !important;
        box-shadow: 4px 0 24px rgba(0,0,0,0.18);
        z-index: 1050 !important;
        transform: translateX(-100%);
        transition: transform 0.3s cubic-bezier(0.4,0,0.2,1), background 0.3s;
    }
    .sidebar.show {
        transform: translateX(0);
    }
    #config-main-content, .main-content {
        margin-left: 0 !important;
        width: 100% !important;
    }
}
@media (min-width: 992px) {
    .sidebar {
        left: 0;
        width: 260px;
        height: 100vh;
        position: fixed;
    }
    #config-main-content, .main-content {
        margin-left: 260px;
        width: calc(100% - 260px);
    }
}
/* Corrigir cores transparentes no tema claro */
.navbar, .navbar-django {
    background: linear-gradient(135deg, var(--nix-primary) 0%, var(--nix-primary-dark) 100%) !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}
.theme-toggle {
    background-color: rgba(58, 61, 92, 0.08);
    border: 1px solid rgba(58, 61, 92, 0.12);
}
.dropdown-menu {
    background-color: var(--bg-secondary) !important;
    box-shadow: 0 4px 16px rgba(0,0,0,0.10);
}
.card {
    background-color: var(--bg-color);
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}
/* Ajustar espaçamentos para responsividade */
@media (max-width: 991.98px) {
    #config-main-content, .main-content {
        margin-left: 0 !important;
        width: 100% !important;
        padding-left: 1rem;
        padding-right: 1rem;
    }

    /* Sidebar responsiva */
    .sidebar {
        width: 280px !important;
    }

    /* Config content responsivo */
    .config-content {
        padding: 1rem 0;
    }

    .config-page-title {
        font-size: 1.75rem;
    }
}

@media (max-width: 768px) {
    #config-main-content, .main-content {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    .config-content {
        padding: 0.75rem 0;
    }

    .config-page-title {
        font-size: 1.5rem;
    }

    /* Formulários de config responsivos */
    .form-group {
        margin-bottom: 1rem;
    }

    .form-label {
        font-size: 0.875rem;
        font-weight: 600;
    }
}
.sidebar-heading {
    font-size: 0.75rem;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.05em;
    color: var(--text-muted);
    font-family: 'Roboto', sans-serif;
    margin-bottom: 0.5rem;
}
.sidebar-heading .badge {
    font-size: 0.65rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    background-color: var(--primary-color) !important;
    color: white;
}
.sidebar-section {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.sidebar-section:last-child {
    border-bottom: none;
    margin-bottom: 1rem;
}
.sidebar-section:first-child {
    border-bottom: 2px solid var(--primary-color);
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
}
.sidebar-section:first-child .nav-link {
    font-weight: 600;
    font-size: 1rem;
    padding: 1rem;
    background: linear-gradient(135deg, var(--primary-color), var(--nix-accent));
    color: white !important;
    border-radius: 0.75rem;
    margin: 0.5rem;
    box-shadow: 0 4px 12px rgba(58, 61, 92, 0.3);
}
.sidebar-section:first-child .nav-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(58, 61, 92, 0.4);
}
.config-content {
    padding: 1rem 0;
}
.config-card {
    transition: all 0.3s ease;
    border-radius: 0.5rem;
}
.config-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.config-page-title {
    border-left: 4px solid var(--nix-primary);
    padding-left: 1rem;
    background: linear-gradient(to right, var(--nix-primary), var(--nix-accent));
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
    font-weight: 600;
}
@media (max-width: 767.98px) {
    .config-content {
        padding: 0.5rem 0;
    }
    .config-page-title {
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }
}

/* Sidebar específica para listagem de artigos */
.articles-list .sidebar {
    position: static !important;
    width: 100% !important;
    max-width: none !important;
    height: auto !important;
    box-shadow: none !important;
    border-right: none !important;
    margin-bottom: 2rem;
}
@media (min-width: 992px) {
    .articles-list .sidebar {
        margin-bottom: 0;
        align-items: flex-end !important;
    }
}

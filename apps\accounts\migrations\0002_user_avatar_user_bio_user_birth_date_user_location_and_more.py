# Generated by Django 5.2.2 on 2025-06-06 19:09

import apps.accounts.models.user
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='avatar',
            field=models.ImageField(blank=True, help_text='Foto de perfil do usuário', null=True, upload_to=apps.accounts.models.user.user_avatar_path, verbose_name='avatar'),
        ),
        migrations.AddField(
            model_name='user',
            name='bio',
            field=models.TextField(blank=True, help_text='Breve descrição sobre o usuário', max_length=500, verbose_name='biografia'),
        ),
        migrations.AddField(
            model_name='user',
            name='birth_date',
            field=models.DateField(blank=True, help_text='Data de nascimento do usuário', null=True, verbose_name='data de nascimento'),
        ),
        migrations.AddField(
            model_name='user',
            name='location',
            field=models.CharField(blank=True, help_text='Cidade/Estado do usuário', max_length=100, verbose_name='localização'),
        ),
        migrations.AddField(
            model_name='user',
            name='phone',
            field=models.CharField(blank=True, help_text='Número de telefone do usuário', max_length=20, verbose_name='telefone'),
        ),
    ]

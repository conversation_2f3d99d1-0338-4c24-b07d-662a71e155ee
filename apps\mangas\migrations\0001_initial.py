# Generated by Django 5.2.2 on 2025-07-22 21:00

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Manga',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='Títu<PERSON>')),
                ('author', models.CharField(blank=True, max_length=120, verbose_name='Autor')),
                ('description', models.TextField(blank=True, verbose_name='Descrição')),
                ('cover_image', models.ImageField(blank=True, null=True, upload_to='mangas/covers/', verbose_name='Capa')),
                ('slug', models.SlugField(blank=True, unique=True, verbose_name='Slug')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Atualizado em')),
            ],
            options={
                'verbose_name': 'Mangá',
                'verbose_name_plural': 'Mangás',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Capitulo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('number', models.PositiveIntegerField(verbose_name='Número do Capítulo')),
                ('title', models.CharField(blank=True, max_length=200, verbose_name='Título')),
                ('slug', models.SlugField(blank=True, unique=True, verbose_name='Slug')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Atualizado em')),
                ('manga', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='capitulos', to='mangas.manga')),
            ],
            options={
                'verbose_name': 'Capítulo',
                'verbose_name_plural': 'Capítulos',
                'ordering': ['number'],
                'unique_together': {('manga', 'number')},
            },
        ),
        migrations.CreateModel(
            name='Pagina',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('number', models.PositiveIntegerField(verbose_name='Número da Página')),
                ('image', models.ImageField(upload_to='mangas/pages/', verbose_name='Imagem')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Atualizado em')),
                ('capitulo', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='paginas', to='mangas.capitulo')),
            ],
            options={
                'verbose_name': 'Página',
                'verbose_name_plural': 'Páginas',
                'ordering': ['number'],
                'unique_together': {('capitulo', 'number')},
            },
        ),
    ]

{% extends 'base.html' %}
{% load static %}

{% block title %}FireFlies - Sistema de Gerenciamento de Conteúdo{% endblock %}

{% block content %}


<!-- Artigos em destaque no topo como carrossel multi-item -->
<div class="container mt-5">
    <div class="row g-4">
        <div class="col-12">
            <h2 class="text-fireflies-glow mb-4 text-center">
                <i class="fas fa-newspaper me-2"></i>Conteúdo em Destaque
            </h2>
            {% if main_articles %}
            <div id="featuredArticlesCarousel" class="carousel slide" data-bs-ride="carousel">
                <div class="carousel-inner">
                    {% for article in main_articles %}
                        {% if forloop.counter0|divisibleby:3 %}
                        <div class="carousel-item {% if forloop.counter0 == 0 %}active{% endif %}">
                            <div class="row row-cols-1 row-cols-md-3 g-4 justify-content-center">
                        {% endif %}
                        <div class="col">
                            <div class="card h-100 border-0 shadow fireflies-glow featured-article-card mx-auto" style="max-width: 400px; transition: transform 0.3s;">
                                {% if article.featured_image %}
                                <img src="{{ article.featured_image.url }}" class="card-img-top featured-article-img" alt="{{ article.featured_image_alt|default:article.title }}">
                                {% endif %}
                                <div class="card-body d-flex flex-column">
                                    <h5 class="card-title text-fireflies">{{ article.title }}</h5>
                                    <p class="card-text text-muted small mb-2">{{ article.excerpt|truncatechars:120 }}</p>
                                    <div class="mt-auto">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-user text-fireflies me-1"></i>
                                            <span class="me-2">{{ article.author.get_full_name|default:article.author.username }}</span>
                                            <i class="fas fa-calendar-alt text-fireflies me-1"></i>
                                            <span>{{ article.published_at|date:'d M Y' }}</span>
                                        </div>
                                        <a href="{{ article.get_absolute_url }}" class="btn btn-outline-primary btn-sm w-100">
                                            <i class="fas fa-arrow-right me-1"></i>Ler artigo completo
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% if forloop.counter|divisibleby:3 or forloop.last %}
                            </div>
                        </div>
                        {% endif %}
                    {% endfor %}
                </div>
                <button class="carousel-control-prev" type="button" data-bs-target="#featuredArticlesCarousel" data-bs-slide="prev">
                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">Anterior</span>
                </button>
                <button class="carousel-control-next" type="button" data-bs-target="#featuredArticlesCarousel" data-bs-slide="next">
                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">Próximo</span>
                </button>
            </div>
            {% else %}
            <div class="alert alert-info text-center my-4">
                <i class="fas fa-info-circle me-2"></i>Nenhum artigo publicado ainda. Que tal ser o primeiro a contribuir?
            </div>
            {% endif %}
        </div>
    </div>
</div>

{% block extra_css %}
{{ block.super }}
<style>
.featured-article-card:hover {
    transform: scale(1.05);
    z-index: 2;
    box-shadow: 0 0.5rem 2rem rgba(0,0,0,0.15) !important;
}
/* Padroniza o tamanho das imagens dos cards de destaque */
.featured-article-img {
    width: 100%;
    height: 220px;
    object-fit: cover;
    object-position: center;
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;
    background: #f8f9fa;
}
</style>
{% endblock %}

<!-- FireFlies Particles Effect -->
<div class="fireflies-particles">
    <div class="fireflies-particle" style="left: 10%; animation-delay: 0s;"></div>
    <div class="fireflies-particle" style="left: 20%; animation-delay: 1s;"></div>
    <div class="fireflies-particle" style="left: 30%; animation-delay: 2s;"></div>
    <div class="fireflies-particle" style="left: 40%; animation-delay: 3s;"></div>
    <div class="fireflies-particle" style="left: 50%; animation-delay: 4s;"></div>
    <div class="fireflies-particle" style="left: 60%; animation-delay: 5s;"></div>
    <div class="fireflies-particle" style="left: 70%; animation-delay: 6s;"></div>
    <div class="fireflies-particle" style="left: 80%; animation-delay: 7s;"></div>
    <div class="fireflies-particle" style="left: 90%; animation-delay: 8s;"></div>
</div>

<div class="container my-5">
    <div class="row">
        <div class="col-12">
            <div class="text-center mb-5">
                <img src="/static/favicon-32x32.png" alt="FireFlies Logo" width="32" height="32" class="me-2">
                
            </div>
            
            <div class="row g-4">
                <!-- Hero Section -->
                <div class="col-lg-8 mx-auto text-center">
                    <h2 class="display-6 fw-bold text-sans text-fireflies">Sistema FireFlies</h2>
                    <p class="lead text-muted mb-4">
                        Uma plataforma moderna e robusta para gerenciamento de conteúdo, 
                        desenvolvida com as melhores práticas de arquitetura limpa.
                        <br>
                        <span class="text-fireflies-glow">Iluminando o caminho do desenvolvimento de software</span>
                    </p>
                    
                    <div class="d-flex justify-content-center gap-3 mb-5">
                        <a href="{% url 'articles:article_list' %}" class="btn btn-primary btn-lg fireflies-glow">
                            <i class="fas fa-newspaper me-2"></i>Ver Artigos
                        </a>
                        <a href="{% url 'pages:about' %}" class="btn btn-accent btn-lg fireflies-glow">
                            <i class="fas fa-info-circle me-2"></i>Saiba Mais
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Features Grid -->
            <div class="row g-4 mt-5">
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm fireflies-glow">
                        <div class="card-body text-center p-4">
                            <div class="mb-3">
                                <img src="/static/favicon-32x32.png" alt="FireFlies Logo" width="48" height="48" class="fireflies-icon">
                            </div>
                            <h5 class="card-title text-sans text-fireflies">Arquitetura Limpa</h5>
                            <p class="card-text text-muted">
                                Desenvolvido seguindo os princípios SOLID e padrões de design modernos.
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm fireflies-glow">
                        <div class="card-body text-center p-4">
                            <div class="mb-3">
                                <i class="fas fa-shield-alt fa-3x text-fireflies fireflies-icon"></i>
                            </div>
                            <h5 class="card-title text-sans text-fireflies">Segurança</h5>
                            <p class="card-text text-muted">
                                Sistema de autenticação robusto com múltiplas camadas de proteção.
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm fireflies-glow">
                        <div class="card-body text-center p-4">
                            <div class="mb-3">
                                <i class="fas fa-lightbulb fa-3x text-fireflies fireflies-icon"></i>
                            </div>
                            <h5 class="card-title text-sans text-fireflies">Inspiração & Colaboração</h5>
                            <p class="card-text text-muted">
                               
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Call to Action -->
            <div class="row mt-5">
                <div class="col-12 text-center">
                    <div class="card border-0 shadow-sm fireflies-glow">
                        <div class="card-body p-5">
                            <h3 class="text-fireflies-glow mb-3">Pronto para começar?</h3>
                            <p class="text-muted mb-4">
                                Explore nossa plataforma
                            </p>
                            <div class="d-flex justify-content-center gap-3">
                                
                                <a href="{% url 'pages:about' %}" class="btn btn-outline-primary btn-lg">
                                    <i class="fas fa-info-circle me-2"></i>Saiba Mais
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

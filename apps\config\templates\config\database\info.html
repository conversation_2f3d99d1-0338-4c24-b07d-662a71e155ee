{% extends 'config/base_config.html' %}
{% load crispy_forms_tags %}
{% load config_extras %}

{% block title %}Informações do Banco de Dados - {{ block.super }}{% endblock %}

{% block config_content %}
<div class="container-fluid">
    <!-- Header da Página -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-1 text-sans text-body">
                        <i class="fas fa-database me-2 text-django-green"></i>Informações do Banco de Dados
                    </h1>
                    <p class="text-secondary mb-0 text-body">Status e configurações atuais do banco de dados</p>
                </div>
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-info" onclick="testConnection()">
                        <i class="fas fa-vial me-2"></i>Testar Conexão
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="location.reload()">
                        <i class="fas fa-sync me-2"></i>Atualizar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Status da Conexão -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-django border-0 shadow-sm">
                <div class="card-body">
                    <h5 class="card-title text-sans text-body">
                        <i class="fas fa-plug me-2 text-django-green"></i>Status da Conexão
                    </h5>
                    
                    {% if connection_status.status == 'success' %}
                        <div class="alert alert-success d-flex align-items-center" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <div>
                                <strong>Conexão Ativa!</strong> {{ connection_status.message }}
                            </div>
                        </div>
                        
                        {% if connection_status.details.version %}
                            <p class="mb-0"><strong>Versão:</strong> {{ connection_status.details.version }}</p>
                        {% endif %}
                        
                        {% if connection_status.details.file_exists %}
                            <p class="mb-0"><strong>Arquivo:</strong> Existe ({{ connection_status.details.file_size|filesizeformat }})</p>
                        {% endif %}
                        
                    {% else %}
                        <div class="alert alert-danger d-flex align-items-center" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <div>
                                <strong>Erro de Conexão!</strong> {{ connection_status.message }}
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Informações do Banco -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card-django border-0 shadow-sm">
                <div class="card-body">
                    <h5 class="card-title text-sans text-body">
                        <i class="fas fa-info-circle me-2 text-django-green"></i>Configuração Atual
                    </h5>
                    
                    {% if db_info.error %}
                        <div class="alert alert-warning">{{ db_info.error }}</div>
                    {% else %}
                        <div class="table-responsive">
                            <table class="table table-borderless mb-0">
                                <tr>
                                    <td><strong>Tipo:</strong></td>
                                    <td>
                                        {% if db_info.is_sqlite %}
                                            <span class="badge bg-primary">SQLite</span>
                                        {% elif db_info.is_postgresql %}
                                            <span class="badge bg-info">PostgreSQL</span>
                                        {% elif db_info.is_mysql %}
                                            <span class="badge bg-warning">MySQL</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ db_info.type }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Engine:</strong></td>
                                    <td><code>{{ db_info.engine }}</code></td>
                                </tr>
                                <tr>
                                    <td><strong>Nome:</strong></td>
                                    <td>{{ db_info.name }}</td>
                                </tr>
                                {% if db_info.host %}
                                <tr>
                                    <td><strong>Host:</strong></td>
                                    <td>{{ db_info.host }}</td>
                                </tr>
                                {% endif %}
                                {% if db_info.port %}
                                <tr>
                                    <td><strong>Porta:</strong></td>
                                    <td>{{ db_info.port }}</td>
                                </tr>
                                {% endif %}
                                {% if db_info.user %}
                                <tr>
                                    <td><strong>Usuário:</strong></td>
                                    <td>{{ db_info.user }}</td>
                                </tr>
                                {% endif %}
                                {% if db_info.file_path %}
                                <tr>
                                    <td><strong>Arquivo:</strong></td>
                                    <td><code>{{ db_info.file_path }}</code></td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card-django border-0 shadow-sm">
                <div class="card-body">
                    <h5 class="card-title text-sans text-body">
                        <i class="fas fa-file-code me-2 text-django-green"></i>Arquivo .env
                    </h5>
                    
                    {% if env_info.exists %}
                        <div class="alert alert-success d-flex align-items-center" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <div>Arquivo .env encontrado</div>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-borderless mb-0">
                                <tr>
                                    <td><strong>DATABASE_URL:</strong></td>
                                    <td>
                                        {% if env_info.has_database_url %}
                                            <span class="badge bg-success">Configurado</span>
                                        {% else %}
                                            <span class="badge bg-warning">Não configurado</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>DB_ENGINE:</strong></td>
                                    <td>
                                        {% if env_info.has_db_engine %}
                                            <span class="badge bg-success">Configurado</span>
                                        {% else %}
                                            <span class="badge bg-warning">Não configurado</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                        
                        {% if env_info.variables %}
                        <details class="mt-3">
                            <summary class="text-muted">Ver todas as variáveis de banco</summary>
                            <div class="mt-2">
                                {% for key, value in env_info.variables.items %}
                                <div class="small">
                                    <code>{{ key }}={{ value }}</code>
                                </div>
                                {% endfor %}
                            </div>
                        </details>
                        {% endif %}
                        
                    {% else %}
                        <div class="alert alert-warning d-flex align-items-center" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <div>{{ env_info.message }}</div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Aviso sobre Configuração -->
    <div class="row">
        <div class="col-12">
            <div class="alert alert-info d-flex align-items-center" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                <div>
                    <strong>Configuração de Banco de Dados</strong><br>
                    A configuração do banco de dados é feita apenas uma vez durante o setup inicial do sistema. 
                    Para alterar a configuração, use o <a href="{% url 'config:setup_wizard' %}" class="alert-link">Wizard de Setup</a>.
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container my-4">
    <h1 class="h3 mb-4 text-sans text-body">
        <i class="fas fa-database me-2 text-django-green"></i>Configurações de Banco de Dados
    </h1>
    <div class="card-django border-0 shadow-sm mb-4">
        <div class="card-body p-0">
            {% with headers=[
                {'label': 'Nome'},
                {'label': 'Engine'},
                {'label': 'Banco'},
                {'label': 'Status'}
            ] %}
            {% with rows=db_configs|map:'db_config_row' %}
                {% include 'config/includes/_table.html' with headers=headers rows=rows caption='Tabela de configurações de banco de dados' %}
            {% endwith %}
            {% endwith %}
        </div>
    </div>
</div>

<script>
function testConnection() {
    const button = event.target;
    const originalText = button.innerHTML;
    
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Testando...';
    button.disabled = true;
    
    fetch('{% url "config:database_connection_test" %}', {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showAlert('success', 'Conexão bem-sucedida!', data.message);
        } else {
            showAlert('danger', 'Erro na conexão!', data.message);
        }
    })
    .catch(error => {
        showAlert('danger', 'Erro no teste!', 'Erro ao executar o teste de conexão.');
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

function showAlert(type, title, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <strong>${title}</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>

{% csrf_token %}
{% endblock %} 
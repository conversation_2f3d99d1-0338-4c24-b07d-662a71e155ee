{% extends "base.html" %}
{% load static %}
{% load i18n %}

{% block title %}Comentários - {{ article.title }}{% endblock %}

{% block extra_css %}
<style>
.comment-item {
    border-left: 3px solid var(--border-color);
    padding-left: 1rem;
    margin-bottom: 1.5rem;
}

.comment-item.reply {
    margin-left: 2rem;
    border-left-color: var(--secondary-color);
    background-color: var(--bg-secondary);
    padding: 1rem;
    border-radius: 0.375rem;
}

.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.comment-author {
    font-weight: 600;
    color: var(--text-color);
}

.comment-date {
    font-size: 0.875rem;
    color: var(--text-muted);
}

.comment-content {
    margin-bottom: 1rem;
    line-height: 1.6;
}

.comment-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.reply-form {
    display: none;
    margin-top: 1rem;
    padding: 1rem;
    background-color: var(--bg-secondary);
    border-radius: 0.375rem;
    border: 1px solid var(--border-color);
}

.reply-form.show {
    display: block;
}

.comment-form {
    background-color: var(--bg-color);
    color: var(--text-color);
    padding: 2rem;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 2rem;
}

.form-floating {
    margin-bottom: 1rem;
}

.btn-reply {
    font-size: 0.875rem;
    padding: 0.25rem 0.75rem;
}

.comment-count {
    color: var(--text-muted);
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
}

.no-comments {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-muted);
}

.loading {
    text-align: center;
    padding: 2rem;
}

.alert-comment {
    margin-bottom: 1rem;
}

.comment-count, .no-comments, .form-text {
    color: var(--text-muted);
}

.fade-in-feedback {
    animation: fadeIn 0.7s;
}
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
</style>
{% endblock %}

{% block content %}
<div class="container mt-4" role="main">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'articles:article_list' %}">Artigos</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'articles:article_detail' article.slug %}">{{ article.title|truncatechars:50 }}</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Comentários</li>
                </ol>
            </nav>

            <!-- Título do artigo -->
            <div class="mb-4">
                <h1 class="h3">Comentários</h1>
                <p class="text-muted">
                    <a href="{% url 'articles:article_detail' article.slug %}" class="text-decoration-none">
                        {{ article.title }}
                    </a>
                </p>
            </div>

            <!-- Contador de comentários -->
            <div class="comment-count">
                <i class="fas fa-comments"></i>
                {% if comments.paginator.count == 0 %}
                    Nenhum comentário ainda
                {% elif comments.paginator.count == 1 %}
                    1 comentário
                {% else %}
                    {{ comments.paginator.count }} comentários
                {% endif %}
            </div>

            <!-- Formulário de comentário -->
            {% if article.allow_comments %}
            <div class="comment-form" aria-label="Formulário de comentário" role="form">
                <h4 class="mb-3">
                    <i class="fas fa-edit"></i>
                    {% trans "Deixe seu comentário" %}
                </h4>
                
                <form method="post" action="{% url 'articles:add_comment' article.slug %}" id="comment-form" aria-label="Formulário de comentário" role="form">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating">
                                {{ comment_form.name.label_tag }}
                                {{ comment_form.name }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                {{ comment_form.email.label_tag }}
                                {{ comment_form.email }}
                            </div>
                        </div>
                    </div>
                    <div class="form-floating">
                        {{ comment_form.website.label_tag }}
                        {{ comment_form.website }}
                    </div>
                    <div class="form-floating">
                        {{ comment_form.content.label_tag }}
                        {{ comment_form.content }}
                    </div>
                    
                    <!-- Honeypot -->
                    {{ comment_form.website_url }}
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            Seu comentário será moderado antes da publicação.
                        </small>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i>
                            {% trans "Enviar Comentário" %}
                        </button>
                    </div>
                </form>
            </div>
            {% else %}
            <div class="alert alert-info">
                <i class="fas fa-lock"></i>
                Comentários estão desabilitados para este artigo.
            </div>
            {% endif %}

            <!-- Lista de comentários -->
            <div id="comments-list">
                {% if comments %}
                    {% for comment in comments %}
                    <div class="comment-item" id="comment-{{ comment.id }}">
                        <div class="comment-header">
                            <div class="comment-author">
                                {% if comment.website %}
                                    <a href="{{ comment.website }}" target="_blank" rel="nofollow noopener" class="text-decoration-none">
                                        <i class="fas fa-user"></i>
                                        {{ comment.author_name }}
                                        <i class="fas fa-external-link-alt fa-xs"></i>
                                    </a>
                                {% else %}
                                    <i class="fas fa-user"></i>
                                    {{ comment.author_name }}
                                {% endif %}
                                {% if comment.user.is_staff %}
                                    <span class="badge bg-theme-primary ms-1">Staff</span>
                                {% endif %}
                            </div>
                            <div class="comment-date">
                                <i class="fas fa-clock"></i>
                                {{ comment.created_at|date:"d/m/Y H:i" }}
                            </div>
                        </div>
                        
                        <div class="comment-content">
                            {{ comment.content|linebreaks }}
                        </div>
                        
                        <div class="comment-actions">
                            {% if article.allow_comments and comment.can_be_replied %}
                                <button class="btn btn-outline-secondary btn-reply btn-sm" 
                                        onclick="toggleReplyForm({{ comment.id }})">
                                    <i class="fas fa-reply"></i>
                                    Responder
                                </button>
                            {% endif %}
                            
                            {% if comment.reply_count > 0 %}
                                <span class="text-muted">
                                    <i class="fas fa-comments"></i>
                                    {{ comment.reply_count }} resposta{{ comment.reply_count|pluralize }}
                                </span>
                            {% endif %}
                        </div>
                        
                        <!-- Formulário de resposta -->
                        {% if article.allow_comments and comment.can_be_replied %}
                        <div class="reply-form" id="reply-form-{{ comment.id }}">
                            <h6>Responder a {{ comment.author_name }}</h6>
                            <form method="post" action="{% url 'articles:add_reply' article.slug comment.id %}" class="reply-form-submit">
                                {% csrf_token %}
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-floating mb-2">
                                            <input type="text" class="form-control form-control-sm" name="name" 
                                                   placeholder="Seu nome" required
                                                   {% if user.is_authenticated %}value="{{ user.get_full_name|default:user.username }}" readonly{% endif %}>
                                            <label>Nome</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating mb-2">
                                            <input type="email" class="form-control form-control-sm" name="email" 
                                                   placeholder="Seu email" required
                                                   {% if user.is_authenticated %}value="{{ user.email }}" readonly{% endif %}>
                                            <label>Email</label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-floating mb-2">
                                    <textarea class="form-control form-control-sm" name="content" 
                                              placeholder="Sua resposta" rows="3" required></textarea>
                                    <label>Resposta</label>
                                </div>
                                
                                <div class="d-flex justify-content-end gap-2">
                                    <button type="button" class="btn btn-secondary btn-sm" 
                                            onclick="toggleReplyForm({{ comment.id }})">
                                        Cancelar
                                    </button>
                                    <button type="submit" class="btn btn-primary btn-sm">
                                        <i class="fas fa-paper-plane"></i>
                                        Enviar Resposta
                                    </button>
                                </div>
                            </form>
                        </div>
                        {% endif %}
                        
                        <!-- Respostas -->
                        {% for reply in comment.get_replies %}
                        <div class="comment-item reply" id="comment-{{ reply.id }}">
                            <div class="comment-header">
                                <div class="comment-author">
                                    <i class="fas fa-reply fa-sm"></i>
                                    {% if reply.website %}
                                        <a href="{{ reply.website }}" target="_blank" rel="nofollow noopener" class="text-decoration-none">
                                            {{ reply.author_name }}
                                            <i class="fas fa-external-link-alt fa-xs"></i>
                                        </a>
                                    {% else %}
                                        {{ reply.author_name }}
                                    {% endif %}
                                    {% if reply.user.is_staff %}
                                        <span class="badge bg-theme-primary ms-1">Staff</span>
                                    {% endif %}
                                </div>
                                <div class="comment-date">
                                    <i class="fas fa-clock"></i>
                                    {{ reply.created_at|date:"d/m/Y H:i" }}
                                </div>
                            </div>
                            
                            <div class="comment-content">
                                {{ reply.content|linebreaks }}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% endfor %}
                    
                    <!-- Paginação -->
                    {% if comments.has_other_pages %}
                    <nav aria-label="Paginação de comentários" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if comments.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ comments.previous_page_number }}">
                                        <i class="fas fa-chevron-left"></i>
                                        Anterior
                                    </a>
                                </li>
                            {% endif %}
                            
                            {% for num in comments.paginator.page_range %}
                                {% if comments.number == num %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ num }}</span>
                                    </li>
                                {% elif num > comments.number|add:'-3' and num < comments.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if comments.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ comments.next_page_number }}">
                                        Próxima
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    
                {% else %}
                    <div class="no-comments">
                        <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Nenhum comentário ainda</h5>
                        <p class="text-muted">Seja o primeiro a comentar neste artigo!</p>
                    </div>
                {% endif %}
            </div>

            {% if messages %}
                <div class="fade-in-feedback">
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-comment">{{ message }}</div>
                    {% endfor %}
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function toggleReplyForm(commentId) {
    const replyForm = document.getElementById(`reply-form-${commentId}`);
    replyForm.classList.toggle('show');
    
    if (replyForm.classList.contains('show')) {
        // Focar no textarea
        const textarea = replyForm.querySelector('textarea[name="content"]');
        if (textarea) {
            textarea.focus();
        }
    }
}

// Submissão AJAX dos formulários
document.addEventListener('DOMContentLoaded', function() {
    // Formulário principal de comentário
    const commentForm = document.getElementById('comment-form');
    if (commentForm) {
        commentForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitForm(this, 'comment');
        });
    }
    
    // Formulários de resposta
    const replyForms = document.querySelectorAll('.reply-form-submit');
    replyForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            submitForm(this, 'reply');
        });
    });
});

function submitForm(form, type) {
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    // Mostrar loading
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Enviando...';
    submitBtn.disabled = true;
    
    fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': formData.get('csrfmiddlewaretoken')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Mostrar mensagem de sucesso
            showAlert('success', data.message);
            
            // Limpar formulário
            if (type === 'comment') {
                form.reset();
            } else {
                // Fechar formulário de resposta
                const replyForm = form.closest('.reply-form');
                replyForm.classList.remove('show');
                form.reset();
            }
            
            // Recarregar comentários se aprovado automaticamente
            if (data.is_approved) {
                setTimeout(() => {
                    location.reload();
                }, 1500);
            }
        } else {
            // Mostrar erros
            let errorMessage = 'Erro ao enviar. Verifique os dados.';
            if (data.errors) {
                const errors = Object.values(data.errors).flat();
                errorMessage = errors.join(' ');
            }
            showAlert('danger', errorMessage);
        }
    })
    .catch(error => {
        console.error('Erro:', error);
        showAlert('danger', 'Erro de conexão. Tente novamente.');
    })
    .finally(() => {
        // Restaurar botão
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-comment`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Inserir no topo da página
    const container = document.querySelector('.container');
    container.insertBefore(alertDiv, container.firstChild);
    
    // Auto-remover após 5 segundos
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}

/* 
 * CSS específico para detalhes de artigos
 * Separado seguindo princípios de organização
 */

/* Article Content Styling */
.article-content .content-wrapper {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.7;
    color: #333;
    max-width: 100%;
    overflow-wrap: break-word;
}

.article-content .content-wrapper h1,
.article-content .content-wrapper h2,
.article-content .content-wrapper h3,
.article-content .content-wrapper h4,
.article-content .content-wrapper h5,
.article-content .content-wrapper h6 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    font-weight: 600;
    line-height: 1.3;
    color: #2c3e50;
}

.article-content .content-wrapper h2 {
    font-size: 1.75rem;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 0.5rem;
}

.article-content .content-wrapper h3 {
    font-size: 1.5rem;
}

.article-content .content-wrapper p {
    margin-bottom: 1.5rem;
    text-align: justify;
}

.article-content .content-wrapper img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    margin: 1.5rem 0;
}

.article-content .content-wrapper figure {
    margin: 2rem 0;
    text-align: center;
}

.article-content .content-wrapper figcaption {
    font-size: 0.9rem;
    color: #6c757d;
    font-style: italic;
    margin-top: 0.5rem;
}

.article-content .content-wrapper blockquote {
    border-left: 4px solid #007bff;
    padding-left: 1.5rem;
    margin: 2rem 0;
    font-style: italic;
    background-color: #f8f9fa;
    padding: 1rem 1.5rem;
    border-radius: 0 8px 8px 0;
}

.article-content .content-wrapper ul,
.article-content .content-wrapper ol {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
}

.article-content .content-wrapper li {
    margin-bottom: 0.5rem;
}

.article-content .content-wrapper a {
    color: #007bff;
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: border-color 0.2s;
}

.article-content .content-wrapper a:hover {
    border-bottom-color: #007bff;
}

.article-content .content-wrapper code {
    background-color: #f8f9fa;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-family: 'Monaco', 'Consolas', monospace;
    font-size: 0.9rem;
}

.article-content .content-wrapper pre {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    overflow-x: auto;
    margin: 1.5rem 0;
}

.article-content .content-wrapper table {
    width: 100%;
    border-collapse: collapse;
    margin: 1.5rem 0;
}

.article-content .content-wrapper th,
.article-content .content-wrapper td {
    border: 1px solid #dee2e6;
    padding: 0.75rem;
    text-align: left;
}

.article-content .content-wrapper th {
    background-color: #f8f9fa;
    font-weight: 600;
}

/* Article Header */
.article-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.article-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 1rem;
    font-size: 0.9rem;
    color: #6c757d;
}

.article-meta .meta-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* Article Actions */
.article-actions {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

.article-actions .btn {
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

/* Comments Section */
.comments-section {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 2px solid #e9ecef;
}

.comment-item {
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.comment-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: #6c757d;
}

.comment-content {
    line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .article-content .content-wrapper {
        font-size: 1rem;
    }
    
    .article-content .content-wrapper h2 {
        font-size: 1.5rem;
    }
    
    .article-content .content-wrapper h3 {
        font-size: 1.25rem;
    }
    
    .article-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .article-actions .btn {
        width: 100%;
        margin-right: 0;
    }
}

/* Print Styles */
@media print {
    .article-actions,
    .comments-section {
        display: none;
    }
    
    .article-content .content-wrapper {
        font-size: 12pt;
        line-height: 1.5;
    }
    
    .article-content .content-wrapper a {
        color: #000;
        text-decoration: underline;
    }
}

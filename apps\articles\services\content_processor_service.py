"""
Service para processamento de conteúdo de artigos
Segue princípios SOLID para separação de responsabilidades
"""
import re
from typing import Optional
from django.utils.html import strip_tags


class ContentProcessorService:
    """
    Service responsável por processar e limpar conteúdo de artigos
    
    Princípios SOLID aplicados:
    - Single Responsibility: Apenas processa conteúdo
    - Open/Closed: Extensível para novos tipos de processamento
    - Liskov Substitution: Pode ser substituído por outras implementações
    - Interface Segregation: Interface específica para processamento
    - Dependency Inversion: Não depende de implementações concretas
    """
    
    def __init__(self):
        """Inicializa o service com configurações padrão"""
        self.problematic_elements = [
            r'<article[^>]*class="[^"]*single-grid[^"]*"[^>]*>.*?</article>',
            r'<header[^>]*>.*?</header>',
            r'<div[^>]*class="[^"]*widget[^"]*"[^>]*>.*?</div>',
            r'<div[^>]*class="[^"]*achados[^"]*"[^>]*>.*?</div>',
            r'<div[^>]*class="[^"]*block-before-content[^"]*"[^>]*>.*?</div>',
            r'<div[^>]*class="[^"]*by[^"]*"[^>]*>.*?</div>',
            r'<div[^>]*class="[^"]*authors-img[^"]*"[^>]*>.*?</div>',
            r'<div[^>]*class="[^"]*author[^"]*"[^>]*>.*?</div>',
            r'<div[^>]*class="[^"]*time[^"]*"[^>]*>.*?</div>',
            r'<p[^>]*class="[^"]*flipboard-subtitle[^"]*"[^>]*>.*?</p>',
            r'<p[^>]*class="[^"]*olho[^"]*"[^>]*>.*?</p>',
        ]
        
        self.unwanted_attributes = [
            r'class="[^"]*"',
            r'data-[^=]*="[^"]*"',
            r'style="[^"]*"',
            r'target="_blank"',
            r'rel="[^"]*"',
            r'title="[^"]*"',
            r'alt="[^"]*"',
        ]
    
    def clean_content(self, content: str) -> str:
        """
        Limpa o conteúdo removendo elementos problemáticos
        
        Args:
            content: Conteúdo HTML bruto
            
        Returns:
            Conteúdo HTML limpo
        """
        if not content:
            return ""
        
        # Remove elementos problemáticos
        cleaned_content = self._remove_problematic_elements(content)
        
        # Remove atributos desnecessários
        cleaned_content = self._remove_unwanted_attributes(cleaned_content)
        
        # Limpa espaços e tags vazias
        cleaned_content = self._clean_whitespace_and_empty_tags(cleaned_content)
        
        return cleaned_content.strip()
    
    def extract_clean_excerpt(self, content: str, max_length: int = 160) -> str:
        """
        Extrai um excerpt limpo do conteúdo
        
        Args:
            content: Conteúdo HTML
            max_length: Tamanho máximo do excerpt
            
        Returns:
            Excerpt limpo sem HTML
        """
        if not content:
            return ""
        
        # Remove todo HTML
        clean_text = strip_tags(content)
        
        # Remove quebras de linha e espaços extras
        clean_text = re.sub(r'\s+', ' ', clean_text).strip()
        
        # Trunca se necessário
        if len(clean_text) > max_length:
            clean_text = clean_text[:max_length].rsplit(' ', 1)[0] + '...'
        
        return clean_text
    
    def _remove_problematic_elements(self, content: str) -> str:
        """Remove elementos HTML problemáticos"""
        for pattern in self.problematic_elements:
            content = re.sub(pattern, '', content, flags=re.DOTALL | re.IGNORECASE)
        return content
    
    def _remove_unwanted_attributes(self, content: str) -> str:
        """Remove atributos HTML desnecessários"""
        for pattern in self.unwanted_attributes:
            content = re.sub(pattern, '', content)
        return content
    
    def _clean_whitespace_and_empty_tags(self, content: str) -> str:
        """Limpa espaços extras e tags vazias"""
        # Remove espaços extras
        content = re.sub(r'\s+', ' ', content)
        content = re.sub(r'>\s+<', '><', content)
        
        # Remove tags vazias (exceto img, br, hr)
        content = re.sub(r'<(?!img|br|hr)([^>]+)>\s*</\1>', '', content)
        
        # Remove linhas vazias
        content = re.sub(r'\n\s*\n', '\n', content)
        
        return content
    
    def format_for_display(self, content: str) -> str:
        """
        Formata conteúdo para exibição otimizada
        
        Args:
            content: Conteúdo HTML
            
        Returns:
            Conteúdo formatado para exibição
        """
        if not content:
            return ""
        
        # Aplica limpeza completa
        formatted_content = self.clean_content(content)
        
        # Adiciona classes Bootstrap para melhor formatação
        formatted_content = self._add_bootstrap_classes(formatted_content)
        
        return formatted_content
    
    def _add_bootstrap_classes(self, content: str) -> str:
        """Adiciona classes Bootstrap para melhor formatação"""
        # Adiciona classes para imagens
        content = re.sub(r'<img([^>]*)>', r'<img\1 class="img-fluid rounded">', content)
        
        # Adiciona classes para tabelas
        content = re.sub(r'<table([^>]*)>', r'<table\1 class="table table-striped">', content)
        
        # Adiciona classes para blockquotes
        content = re.sub(r'<blockquote([^>]*)>', r'<blockquote\1 class="blockquote">', content)
        
        return content


class ArticleContentProcessor:
    """
    Facade para processamento de conteúdo de artigos
    Implementa o padrão Facade para simplificar o uso do ContentProcessorService
    """
    
    def __init__(self, processor_service: Optional[ContentProcessorService] = None):
        """
        Inicializa o processor com injeção de dependência
        
        Args:
            processor_service: Service de processamento (opcional)
        """
        self.processor = processor_service or ContentProcessorService()
    
    def process_article_content(self, content: str) -> str:
        """
        Processa conteúdo de artigo para exibição
        
        Args:
            content: Conteúdo HTML bruto
            
        Returns:
            Conteúdo processado e limpo
        """
        return self.processor.format_for_display(content)
    
    def generate_excerpt(self, content: str, max_length: int = 160) -> str:
        """
        Gera excerpt limpo do conteúdo
        
        Args:
            content: Conteúdo HTML
            max_length: Tamanho máximo
            
        Returns:
            Excerpt limpo
        """
        return self.processor.extract_clean_excerpt(content, max_length)

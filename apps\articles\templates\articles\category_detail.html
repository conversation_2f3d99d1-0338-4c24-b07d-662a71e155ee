{% extends "base.html" %}

{% block title %}Categoria: {{ category.name }} | {{ block.super }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1 class="mb-3">Categoria: {{ category.name }}</h1>
    {% if category.description %}
        <p class="text-muted">{{ category.description }}</p>
    {% endif %}

    {% if articles %}
        <div class="row">
            {% for article in articles %}
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100">
                        {% if article.featured_image %}
                            <img src="{{ article.featured_image.url }}" class="card-img-top" alt="{{ article.featured_image_alt|default:article.title }}">
                        {% endif %}
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title">
                                <a href="{{ article.get_absolute_url }}">{{ article.title }}</a>
                            </h5>
                            <p class="card-text">{{ article.excerpt|truncatewords:30 }}</p>
                        </div>
                        <div class="card-footer text-muted small">
                            Publicado em {{ article.published_at|date:"d/m/Y H:i" }}
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="alert alert-info mt-4">
            Nenhum artigo encontrado nesta categoria.
        </div>
    {% endif %}

    <a href="{% url 'articles:category_list' %}" class="btn btn-secondary mt-3">Ver todas as categorias</a>
</div>
{% endblock %} 
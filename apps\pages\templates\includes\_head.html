{% load static %}
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=edge">

<!-- Title -->
<title>{% block title %}{{ meta_title|default:"Project Nix" }}{% endblock %}</title>

<!-- Meta Tags -->
<meta name="description" content="{{ meta_description|default:'Leitura de artigos, livros, mangás e audiolivros no Project Nix' }}">
{% if meta_keywords %}<meta name="keywords" content="{{ meta_keywords }}">{% endif %}
<meta name="author" content="Project Nix Team">
<meta name="robots" content="index, follow">

<!-- Open Graph / Facebook -->
<meta property="og:type" content="website">
<meta property="og:url" content="{{ request.build_absolute_uri }}">
<meta property="og:title" content="{{ meta_title|default:'Project Nix' }}">
<meta property="og:description" content="{{ meta_description|default:'Leitura de artigos, livros, mangás e audiolivros' }}">
<meta property="og:site_name" content="Project Nix">
{% if og_image %}<meta property="og:image" content="{{ og_image }}">{% endif %}

<!-- Twitter -->
<meta property="twitter:card" content="summary_large_image">
<meta property="twitter:url" content="{{ request.build_absolute_uri }}">
<meta property="twitter:title" content="{{ meta_title|default:'Project Nix' }}">
<meta property="twitter:description" content="{{ meta_description|default:'Leitura de artigos, livros, mangás e audiolivros' }}">
{% if og_image %}<meta property="twitter:image" content="{{ og_image }}">{% endif %}

<!-- Favicon -->
<link rel="icon" type="image/x-icon" href="{% static 'favicon.ico' %}">
<meta name="msapplication-TileColor" content="#2d5a27">
<meta name="theme-color" content="#2d5a27">

<!-- CSS -->
<!-- Bootstrap CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">

<!-- Font Awesome -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous">

<!-- Google Fonts (Django Style) -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,300;0,400;0,500;0,700;1,400;1,700&family=Fira+Mono:wght@400;700&display=swap" rel="stylesheet">

<!-- Custom CSS -->
<link href="{% static 'css/main.css' %}" rel="stylesheet">
<link href="{% static 'css/forms.css' %}" rel="stylesheet">
<link href="{% static 'css/accessibility.css' %}" rel="stylesheet">

<!-- Extra CSS -->
{% block extra_css %}{% endblock %}

<!-- Theme Color -->
<meta name="theme-color" content="#3A3D5C">

<!-- Preload important resources -->
<link rel="preload" href="{% static 'css/main.css' %}" as="style">

<!-- Analytics -->
{% if google_analytics_id %}
<!-- Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id={{ google_analytics_id }}"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', '{{ google_analytics_id }}');
</script>
{% endif %}

{% if google_tag_manager_id %}
<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','{{ google_tag_manager_id }}');</script>
{% endif %}

{% if facebook_pixel_id %}
<!-- Facebook Pixel -->
<script>
!function(f,b,e,v,n,t,s)
{if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};
if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];
s.parentNode.insertBefore(t,s)}(window,document,'script',
'https://connect.facebook.net/en_US/fbevents.js');
fbq('init', '{{ facebook_pixel_id }}');
fbq('track', 'PageView');
</script>
<noscript>
<img height="1" width="1" style="display:none"
src="https://www.facebook.com/tr?id={{ facebook_pixel_id }}&ev=PageView&noscript=1"
alt="Facebook Pixel"/>
</noscript>
{% endif %}

<!-- Extra Head -->
{% block extra_head %}{% endblock %}

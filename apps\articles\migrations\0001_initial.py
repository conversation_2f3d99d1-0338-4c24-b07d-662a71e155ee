# Generated by Django 5.2.2 on 2025-06-06 15:06

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Nome da categoria', max_length=100, unique=True, validators=[django.core.validators.MinLengthValidator(2)], verbose_name='nome')),
                ('slug', models.SlugField(help_text='URL amigável da categoria', max_length=100, unique=True, verbose_name='slug')),
                ('description', models.TextField(blank=True, help_text='Descrição da categoria', verbose_name='descrição')),
                ('color', models.CharField(default='#007bff', help_text='Cor da categoria em hexadecimal (ex: #007bff)', max_length=7, verbose_name='cor')),
                ('icon', models.CharField(blank=True, help_text='Classe do ícone (ex: fas fa-newspaper)', max_length=50, verbose_name='ícone')),
                ('is_active', models.BooleanField(default=True, help_text='Se a categoria está ativa', verbose_name='ativo')),
                ('order', models.PositiveIntegerField(default=0, help_text='Ordem de exibição da categoria', verbose_name='ordem')),
                ('meta_title', models.CharField(blank=True, help_text='Título para SEO (máximo 60 caracteres)', max_length=60, verbose_name='meta título')),
                ('meta_description', models.CharField(blank=True, help_text='Descrição para SEO (máximo 160 caracteres)', max_length=160, verbose_name='meta descrição')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='criado em')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='atualizado em')),
                ('parent', models.ForeignKey(blank=True, help_text='Categoria pai para criar hierarquia', null=True, on_delete=django.db.models.deletion.CASCADE, to='articles.category', verbose_name='categoria pai')),
            ],
            options={
                'verbose_name': 'categoria',
                'verbose_name_plural': 'categorias',
                'ordering': ['order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Article',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Título do artigo', max_length=200, validators=[django.core.validators.MinLengthValidator(5)], verbose_name='título')),
                ('slug', models.SlugField(help_text='URL amigável do artigo', max_length=200, unique=True, verbose_name='slug')),
                ('excerpt', models.TextField(help_text='Resumo do artigo para listagens e SEO', max_length=500, verbose_name='resumo')),
                ('content', models.TextField(help_text='Conteúdo completo do artigo', verbose_name='conteúdo')),
                ('featured_image', models.ImageField(blank=True, help_text='Imagem principal do artigo', upload_to='articles/images/', verbose_name='imagem destacada')),
                ('featured_image_alt', models.CharField(blank=True, help_text='Texto alternativo para acessibilidade', max_length=200, verbose_name='texto alternativo da imagem')),
                ('status', models.CharField(choices=[('draft', 'Rascunho'), ('published', 'Publicado'), ('archived', 'Arquivado'), ('scheduled', 'Agendado')], default='draft', help_text='Status de publicação do artigo', max_length=20, verbose_name='status')),
                ('is_featured', models.BooleanField(default=False, help_text='Se o artigo deve aparecer em destaque', verbose_name='artigo em destaque')),
                ('allow_comments', models.BooleanField(default=True, help_text='Se o artigo permite comentários', verbose_name='permitir comentários')),
                ('meta_title', models.CharField(blank=True, help_text='Título para SEO (máximo 60 caracteres)', max_length=60, verbose_name='meta título')),
                ('meta_description', models.CharField(blank=True, help_text='Descrição para SEO (máximo 160 caracteres)', max_length=160, verbose_name='meta descrição')),
                ('meta_keywords', models.CharField(blank=True, help_text='Palavras-chave separadas por vírgula', max_length=255, verbose_name='palavras-chave')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='criado em')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='atualizado em')),
                ('published_at', models.DateTimeField(blank=True, help_text='Data e hora de publicação', null=True, verbose_name='publicado em')),
                ('scheduled_at', models.DateTimeField(blank=True, help_text='Data e hora para publicação automática', null=True, verbose_name='agendado para')),
                ('view_count', models.PositiveIntegerField(default=0, help_text='Número de visualizações do artigo', verbose_name='visualizações')),
                ('reading_time', models.PositiveIntegerField(default=0, help_text='Tempo estimado de leitura em minutos', verbose_name='tempo de leitura')),
                ('author', models.ForeignKey(help_text='Autor principal do artigo', on_delete=django.db.models.deletion.CASCADE, related_name='authored_articles', to=settings.AUTH_USER_MODEL, verbose_name='autor')),
                ('contributors', models.ManyToManyField(blank=True, help_text='Outros colaboradores do artigo', related_name='contributed_articles', to=settings.AUTH_USER_MODEL, verbose_name='colaboradores')),
                ('category', models.ForeignKey(blank=True, help_text='Categoria principal do artigo', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='articles', to='articles.category', verbose_name='categoria')),
            ],
            options={
                'verbose_name': 'artigo',
                'verbose_name_plural': 'artigos',
                'ordering': ['-published_at', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Comment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Nome do comentarista', max_length=100, verbose_name='nome')),
                ('email', models.EmailField(help_text='Email do comentarista', max_length=254, validators=[django.core.validators.EmailValidator()], verbose_name='email')),
                ('website', models.URLField(blank=True, help_text='Website do comentarista (opcional)', verbose_name='website')),
                ('content', models.TextField(help_text='Conteúdo do comentário', verbose_name='comentário')),
                ('is_approved', models.BooleanField(default=False, help_text='Se o comentário foi aprovado para exibição', verbose_name='aprovado')),
                ('is_spam', models.BooleanField(default=False, help_text='Se o comentário foi marcado como spam', verbose_name='spam')),
                ('ip_address', models.GenericIPAddressField(blank=True, help_text='IP do comentarista', null=True, verbose_name='endereço IP')),
                ('user_agent', models.TextField(blank=True, help_text='Informações do navegador', verbose_name='user agent')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='criado em')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='atualizado em')),
                ('approved_at', models.DateTimeField(blank=True, help_text='Data e hora da aprovação', null=True, verbose_name='aprovado em')),
                ('article', models.ForeignKey(help_text='Artigo comentado', on_delete=django.db.models.deletion.CASCADE, related_name='comments', to='articles.article', verbose_name='artigo')),
                ('parent', models.ForeignKey(blank=True, help_text='Comentário pai para criar thread de respostas', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='replies', to='articles.comment', verbose_name='comentário pai')),
                ('user', models.ForeignKey(blank=True, help_text='Usuário registrado (se aplicável)', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='comments', to=settings.AUTH_USER_MODEL, verbose_name='usuário')),
            ],
            options={
                'verbose_name': 'comentário',
                'verbose_name_plural': 'comentários',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Tag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Nome da tag', max_length=50, unique=True, validators=[django.core.validators.MinLengthValidator(2)], verbose_name='nome')),
                ('slug', models.SlugField(help_text='URL amigável da tag', unique=True, verbose_name='slug')),
                ('description', models.TextField(blank=True, help_text='Descrição da tag', verbose_name='descrição')),
                ('color', models.CharField(default='#6c757d', help_text='Cor da tag em hexadecimal (ex: #6c757d)', max_length=7, verbose_name='cor')),
                ('is_featured', models.BooleanField(default=False, help_text='Se a tag deve aparecer em destaque', verbose_name='destaque')),
                ('meta_title', models.CharField(blank=True, help_text='Título para SEO (máximo 60 caracteres)', max_length=60, verbose_name='meta título')),
                ('meta_description', models.CharField(blank=True, help_text='Descrição para SEO (máximo 160 caracteres)', max_length=160, verbose_name='meta descrição')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='criado em')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='atualizado em')),
            ],
            options={
                'verbose_name': 'tag',
                'verbose_name_plural': 'tags',
                'ordering': ['name'],
                'indexes': [models.Index(fields=['slug'], name='articles_ta_slug_7a73a7_idx'), models.Index(fields=['is_featured'], name='articles_ta_is_feat_c6c5a4_idx')],
            },
        ),
        migrations.AddField(
            model_name='article',
            name='tags',
            field=models.ManyToManyField(blank=True, help_text='Tags relacionadas ao artigo', related_name='articles', to='articles.tag', verbose_name='tags'),
        ),
        migrations.AddIndex(
            model_name='category',
            index=models.Index(fields=['slug'], name='articles_ca_slug_984c4d_idx'),
        ),
        migrations.AddIndex(
            model_name='category',
            index=models.Index(fields=['is_active', 'order'], name='articles_ca_is_acti_8e81fe_idx'),
        ),
        migrations.AddIndex(
            model_name='comment',
            index=models.Index(fields=['article', 'is_approved', '-created_at'], name='articles_co_article_fc16e2_idx'),
        ),
        migrations.AddIndex(
            model_name='comment',
            index=models.Index(fields=['user', '-created_at'], name='articles_co_user_id_95420e_idx'),
        ),
        migrations.AddIndex(
            model_name='comment',
            index=models.Index(fields=['is_approved', '-created_at'], name='articles_co_is_appr_2d7dab_idx'),
        ),
        migrations.AddIndex(
            model_name='comment',
            index=models.Index(fields=['parent', '-created_at'], name='articles_co_parent__881c82_idx'),
        ),
        migrations.AddIndex(
            model_name='article',
            index=models.Index(fields=['slug'], name='articles_ar_slug_452037_idx'),
        ),
        migrations.AddIndex(
            model_name='article',
            index=models.Index(fields=['status', 'published_at'], name='articles_ar_status_7759bd_idx'),
        ),
        migrations.AddIndex(
            model_name='article',
            index=models.Index(fields=['author', '-published_at'], name='articles_ar_author__1f1edd_idx'),
        ),
        migrations.AddIndex(
            model_name='article',
            index=models.Index(fields=['category', '-published_at'], name='articles_ar_categor_82196d_idx'),
        ),
        migrations.AddIndex(
            model_name='article',
            index=models.Index(fields=['is_featured', '-published_at'], name='articles_ar_is_feat_3f99ec_idx'),
        ),
    ]

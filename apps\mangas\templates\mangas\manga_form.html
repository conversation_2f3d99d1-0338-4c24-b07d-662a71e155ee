{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}{% if form.instance.pk %}Editar{% else %}Adicionar{% endif %} Mangá - Project Nix{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm border-0">
                <div class="card-body">
                    <h1 class="h4 mb-4">
                        {% if form.instance.pk %}
                            Editar Mangá
                        {% else %}
                            Adicionar Novo Mangá
                        {% endif %}
                    </h1>
                    
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                    
                    <form method="post" enctype="multipart/form-data" id="mangaForm" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.title.id_for_label }}" class="form-label">Título do Mangá</label>
                            <input type="text" 
                                   name="{{ form.title.name }}" 
                                   class="form-control {% if form.title.errors %}is-invalid{% endif %}" 
                                   id="{{ form.title.id_for_label }}" 
                                   value="{{ form.title.value|default:'' }}" 
                                   required>
                            {% if form.title.help_text %}
                                <div class="form-text">{{ form.title.help_text }}</div>
                            {% endif %}
                            {% if form.title.errors %}
                                <div class="invalid-feedback">
                                    {{ form.title.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.author.id_for_label }}" class="form-label">Autor</label>
                            <input type="text" 
                                   name="{{ form.author.name }}" 
                                   class="form-control {% if form.author.errors %}is-invalid{% endif %}" 
                                   id="{{ form.author.id_for_label }}" 
                                   value="{{ form.author.value|default:'' }}" 
                                   required>
                            {% if form.author.help_text %}
                                <div class="form-text">{{ form.author.help_text }}</div>
                            {% endif %}
                            {% if form.author.errors %}
                                <div class="invalid-feedback">
                                    {{ form.author.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">Descrição</label>
                            <textarea name="{{ form.description.name }}" 
                                     class="form-control {% if form.description.errors %}is-invalid{% endif %}" 
                                     id="{{ form.description.id_for_label }}" 
                                     rows="4">{{ form.description.value|default:'' }}</textarea>
                            {% if form.description.help_text %}
                                <div class="form-text">{{ form.description.help_text }}</div>
                            {% endif %}
                            {% if form.description.errors %}
                                <div class="invalid-feedback">
                                    {{ form.description.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.cover_image.id_for_label }}" class="form-label">Imagem de Capa</label>
                            <input type="file" 
                                   name="{{ form.cover_image.name }}" 
                                   class="form-control {% if form.cover_image.errors %}is-invalid{% endif %}" 
                                   id="{{ form.cover_image.id_for_label }}"
                                   accept="image/*"
                                   {% if not form.instance.pk %}required{% endif %}>
                            <div class="form-text">
                                Envie uma imagem de capa para o mangá. Formatos suportados: JPG, PNG, WEBP.
                            </div>
                            {% if form.cover_image.errors %}
                                <div class="invalid-feedback">
                                    {{ form.cover_image.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="d-flex justify-content-end gap-2 mt-4">
                            <a href="{{ request.META.HTTP_REFERER|default:'' }}{% if not request.META.HTTP_REFERER %}{% url 'mangas:manga_list' %}{% endif %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i> Cancelar
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Salvar Mangá
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Preview da imagem de capa
        const coverImageInput = document.getElementById('id_cover_image');
        const coverPreview = document.createElement('div');
        coverPreview.className = 'mt-2';
        
        if (coverImageInput) {
            // Adicionar elemento de preview após o campo de upload
            coverImageInput.parentNode.insertBefore(coverPreview, coverImageInput.nextSibling);
            
            // Atualizar preview quando uma nova imagem for selecionada
            coverImageInput.addEventListener('change', function() {
                const file = this.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        coverPreview.innerHTML = `
                            <img src="${e.target.result}" class="img-fluid rounded" style="max-height: 200px;" alt="Preview da capa">
                        `;
                    };
                    reader.readAsDataURL(file);
                } else {
                    coverPreview.innerHTML = '';
                }
            });
            
            // Carregar preview se já houver uma imagem
            if (coverImageInput.dataset.initial) {
                coverPreview.innerHTML = `
                    <img src="${coverImageInput.dataset.initial}" class="img-fluid rounded" style="max-height: 200px;" alt="Capa atual">
                    <p class="text-muted small mt-2">Imagem atual. Faça upload de uma nova imagem para substituir.</p>
                `;
            }
        }
    });
</script>
{% endblock %}
{% extends 'config/base_config.html' %}

{% block config_title %}Teste de Email{% endblock %}

{% block extra_head %}
<meta name="csrf-token" content="{{ csrf_token }}">
{% endblock %}

{% block config_content %}
<!-- Header -->
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2 text-sans text-body">
        <i class="fas fa-paper-plane me-2 text-django-green"></i>Teste de Email
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{% url 'config:email_config' %}" class="btn btn-sm btn-outline-primary">
                <i class="fas fa-cog me-1"></i>Configurações
            </a>
        </div>
    </div>
</div>

<!-- Status da Configuração -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-theme-primary">
                    <i class="fas fa-info-circle me-2"></i>Status da Configuração de Email
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="mb-2">
                                {% if email_configured %}
                                    <i class="fas fa-check-circle fa-3x text-theme-success"></i>
                                {% else %}
                                    <i class="fas fa-exclamation-triangle fa-3x text-theme-warning"></i>
                                {% endif %}
                            </div>
                            <h6>
                                {% if email_configured %}
                                    Configurado
                                {% else %}
                                    Não Configurado
                                {% endif %}
                            </h6>
                        </div>
                    </div>
                    <div class="col-md-9">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-2">
                                    <strong>Servidor:</strong>
                                    <span class="ms-2">{{ email_host }}</span>
                                </div>
                                <div class="mb-2">
                                    <strong>Porta:</strong>
                                    <span class="ms-2">{{ email_port }}</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-2">
                                    <strong>Email Padrão:</strong>
                                    <span class="ms-2">{{ default_from_email }}</span>
                                </div>
                                <div class="mb-2">
                                    <strong>Status:</strong>
                                    <span class="badge {% if email_configured %}bg-theme-success{% else %}bg-theme-warning{% endif %} ms-2">
                                        {% if email_configured %}Pronto para uso{% else %}Requer configuração{% endif %}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Dicas de Teste -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            Para testar o envio de emails, utilize as funcionalidades do sistema que disparam emails reais (ex: cadastro, recuperação de senha, notificações). O formulário de teste foi desativado.
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-theme-info">
                    <i class="fas fa-lightbulb me-2"></i>Dicas de Teste
                </h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <div class="mb-3">
                        <strong><i class="fas fa-check text-theme-success me-1"></i>Teste Básico:</strong>
                        <p class="mb-0">Use seu próprio email para verificar se o envio está funcionando.</p>
                    </div>
                    <div class="mb-3">
                        <strong><i class="fas fa-shield-alt text-theme-warning me-1"></i>Spam:</strong>
                        <p class="mb-0">Verifique a pasta de spam se não receber o email.</p>
                    </div>
                    <div class="mb-3">
                        <strong><i class="fas fa-clock text-theme-info me-1"></i>Tempo:</strong>
                        <p class="mb-0">Emails podem levar alguns minutos para chegar.</p>
                    </div>
                    <div class="mb-3">
                        <strong><i class="fas fa-bug text-theme-danger me-1"></i>Problemas:</strong>
                        <p class="mb-0">Verifique os logs do sistema em caso de erro.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

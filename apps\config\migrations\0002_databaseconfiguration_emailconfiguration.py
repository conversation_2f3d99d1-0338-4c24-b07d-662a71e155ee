# Generated by Django 5.2.2 on 2025-06-06 20:29

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('config', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DatabaseConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Nome identificador (ex: PostgreSQL Principal, MySQL Backup)', max_length=100, verbose_name='Nome da Configuração')),
                ('description', models.TextField(blank=True, verbose_name='Descrição')),
                ('engine', models.CharField(choices=[('django.db.backends.postgresql', 'PostgreSQL'), ('django.db.backends.mysql', 'MySQL'), ('django.db.backends.sqlite3', 'SQLite'), ('django.db.backends.oracle', 'Oracle')], default='django.db.backends.postgresql', max_length=200, verbose_name='Engine do Banco')),
                ('name_db', models.CharField(help_text='Nome da base de dados', max_length=200, verbose_name='Nome do Banco')),
                ('user', models.CharField(blank=True, help_text='Usuário para conexão (não necessário para SQLite)', max_length=200, verbose_name='Usuário')),
                ('password', models.CharField(blank=True, help_text='Senha do usuário', max_length=200, verbose_name='Senha')),
                ('host', models.CharField(blank=True, default='localhost', help_text='Endereço do servidor', max_length=200, verbose_name='Host')),
                ('port', models.CharField(blank=True, help_text='Porta de conexão (deixe vazio para padrão)', max_length=10, verbose_name='Porta')),
                ('options', models.JSONField(blank=True, default=dict, help_text='Configurações extras em formato JSON', verbose_name='Opções Adicionais')),
                ('is_active', models.BooleanField(default=True, verbose_name='Ativo')),
                ('is_default', models.BooleanField(default=False, verbose_name='Padrão')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_tested_at', models.DateTimeField(blank=True, null=True)),
                ('last_test_result', models.JSONField(blank=True, default=dict)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='db_configs_created', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Configuração de Banco de Dados',
                'verbose_name_plural': 'Configurações de Banco de Dados',
                'ordering': ['-is_default', '-is_active', 'name'],
            },
        ),
        migrations.CreateModel(
            name='EmailConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Nome identificador para esta configuração (ex: Gmail Principal, Outlook Backup)', max_length=100, verbose_name='Nome da Configuração')),
                ('description', models.TextField(blank=True, help_text='Descrição detalhada desta configuração', verbose_name='Descrição')),
                ('email_backend', models.CharField(default='django.core.mail.backends.smtp.EmailBackend', max_length=200, verbose_name='Backend de Email')),
                ('email_host', models.CharField(help_text='Ex: smtp.gmail.com', max_length=200, verbose_name='Servidor SMTP')),
                ('email_port', models.IntegerField(default=587, help_text='Geralmente 587 (TLS) ou 465 (SSL)', verbose_name='Porta SMTP')),
                ('email_host_user', models.CharField(help_text='Email ou username para autenticação', max_length=200, verbose_name='Usuário SMTP')),
                ('email_host_password', models.CharField(help_text='Senha ou senha de app', max_length=200, verbose_name='Senha SMTP')),
                ('email_use_tls', models.BooleanField(default=True, help_text='Recomendado para a maioria dos servidores', verbose_name='Usar TLS')),
                ('email_use_ssl', models.BooleanField(default=False, help_text='Alternativa ao TLS', verbose_name='Usar SSL')),
                ('default_from_email', models.EmailField(help_text='Email que aparecerá como remetente', max_length=254, verbose_name='Email Remetente')),
                ('email_timeout', models.IntegerField(default=30, help_text='Tempo limite para conexão', verbose_name='Timeout (segundos)')),
                ('is_active', models.BooleanField(default=True, help_text='Se esta configuração está disponível para uso', verbose_name='Ativo')),
                ('is_default', models.BooleanField(default=False, help_text='Se esta é a configuração padrão do sistema', verbose_name='Padrão')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_tested_at', models.DateTimeField(blank=True, null=True, verbose_name='Último Teste')),
                ('last_test_result', models.JSONField(blank=True, default=dict, verbose_name='Resultado do Último Teste')),
                ('emails_sent_count', models.IntegerField(default=0, help_text='Contador de emails enviados com esta configuração', verbose_name='Emails Enviados')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='email_configs_created', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='email_configs_updated', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Configuração de Email',
                'verbose_name_plural': 'Configurações de Email',
                'ordering': ['-is_default', '-is_active', 'name'],
            },
        ),
    ]

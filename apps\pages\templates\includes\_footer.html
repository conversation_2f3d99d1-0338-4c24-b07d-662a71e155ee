{% load static %}

<footer class="footer-django mt-auto">
    <!-- Minimal Footer -->
    <div class="container py-4">
        <div class="row align-items-center">
            <!-- Brand -->
            <div class="col-md-4">
                <div class="d-flex align-items-center">
                    <img src="/static/favicon.ico" alt="Project Nix Logo" width="32" height="32" class="me-2">
                    <span class="fw-bold text-theme-light">Project Nix</span>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="col-md-4 text-center">
                <div class="d-flex justify-content-center gap-3">
                    <a href="{% url 'pages:home' %}" class="text-theme-light text-decoration-none small">Home</a>
                    <a href="{% url 'articles:article_list' %}" class="text-theme-light text-decoration-none small">Artigos</a>
                    <a href="#" class="text-theme-light text-decoration-none small">Livros</a>
                    <a href="#" class="text-theme-light text-decoration-none small">Mangás</a>
                    <a href="#" class="text-theme-light text-decoration-none small">Audiolivros</a>
                    <a href="{% url 'pages:about' %}" class="text-theme-light text-decoration-none small">Sobre</a>
                    <a href="{% url 'pages:contact' %}" class="text-theme-light text-decoration-none small">Contato</a>
                </div>
            </div>

            <!-- Copyright -->
            <div class="col-md-4 text-md-end">
                <p class="mb-0 text-muted small">
                    &copy; {% now "Y" %} Project Nix. Todos os direitos reservados.
                </p>
            </div>
        </div>
    </div>
    
    <!-- Back to Top Button -->
    <button type="button" class="btn btn-primary btn-floating btn-lg fireflies-glow" id="btn-back-to-top" title="Voltar ao topo">
        <i class="fas fa-arrow-up"></i>
    </button>
    {# O JS e CSS do botão agora está em static/js/main.js e static/css/main.css #}
</footer>

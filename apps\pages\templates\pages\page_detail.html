{% extends 'base.html' %}

{% block title %}{{ page.seo_title|default:page.title }} - {{ block.super }}{% endblock %}

{% block meta_description %}{{ page.seo_description|default:page.excerpt|default:"" }}{% endblock %}
{% block meta_keywords %}{{ page.meta_keywords|default:"" }}{% endblock %}

{% block content %}
<div class="container my-5">
    <!-- Breadcrumbs -->
    {% if breadcrumbs %}
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                {% for breadcrumb in breadcrumbs %}
                    {% if breadcrumb.is_current %}
                        <li class="breadcrumb-item active" aria-current="page">
                            {{ breadcrumb.title }}
                        </li>
                    {% else %}
                        <li class="breadcrumb-item">
                            <a href="{{ breadcrumb.url }}" class="text-decoration-none">
                                {{ breadcrumb.title }}
                            </a>
                        </li>
                    {% endif %}
                {% endfor %}
            </ol>
        </nav>
    {% endif %}

    <div class="row">
        <div class="col-lg-8">
            <!-- Page Header -->
            <article class="mb-5">
                <header class="mb-4">
                    <h1 class="display-5 fw-bold text-sans text-body">{{ page.title }}</h1>
                    
                    {% if page.excerpt %}
                        <p class="lead text-secondary text-body">{{ page.excerpt }}</p>
                    {% endif %}
                    
                    <div class="d-flex align-items-center text-secondary mb-3">
                        <small>
                            <i class="fas fa-calendar me-1"></i>
                            Publicado em {{ page.created_at|date:"d/m/Y" }}
                        </small>
                        
                        {% if page.updated_at != page.created_at %}
                            <small class="ms-3">
                                <i class="fas fa-edit me-1"></i>
                                Atualizado em {{ page.updated_at|date:"d/m/Y" }}
                            </small>
                        {% endif %}
                        
                        {% if page.views_count %}
                            <small class="ms-3">
                                <i class="fas fa-eye me-1"></i>
                                {{ page.views_count }} visualizações
                            </small>
                        {% endif %}
                    </div>
                </header>

                <!-- Featured Image -->
                {% if page.featured_image %}
                    <div class="form-section">
                        <img src="{{ page.featured_image.url }}" class="img-fluid rounded" alt="{{ page.title }}">
                    </div>
                {% endif %}

                <!-- Page Content -->
                <div class="page-content">
                    {{ page.content|safe }}
                </div>

                <!-- Page Footer -->
                <footer class="mt-5 pt-4 border-top">
                    <div class="row">
                        <div class="col-md-6">
                            {% if page.tags.all %}
                                <div class="form-group-comfortable">
                                    <strong>Tags:</strong>
                                    {% for tag in page.tags.all %}
                                        <span class="badge badge-enhanced bg-secondary me-1">{{ tag.name }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 text-md-end">
                            <!-- Share Buttons -->
                            <div class="btn-group" role="group" aria-label="Compartilhar">
                                <a href="https://www.facebook.com/sharer/sharer.php?u={{ request.build_absolute_uri }}" 
                                   target="_blank" class="btn btn-outline-primary btn-sm btn-enhanced">
                                    <i class="fab fa-facebook-f"></i>
                                </a>
                                <a href="https://twitter.com/intent/tweet?url={{ request.build_absolute_uri }}&text={{ page.title }}" 
                                   target="_blank" class="btn btn-outline-info btn-sm btn-enhanced">
                                    <i class="fab fa-twitter"></i>
                                </a>
                                <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ request.build_absolute_uri }}" 
                                   target="_blank" class="btn btn-outline-primary btn-sm btn-enhanced">
                                    <i class="fab fa-linkedin-in"></i>
                                </a>
                                <a href="whatsapp://send?text={{ page.title }} {{ request.build_absolute_uri }}" 
                                   class="btn btn-outline-success btn-sm btn-enhanced">
                                    <i class="fab fa-whatsapp"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </footer>
            </article>

            <!-- Child Pages -->
            {% if children %}
                <section class="mb-5">
                    <h3 class="h4 mb-3 text-sans text-body">
                        <i class="fas fa-sitemap me-2"></i>Subpáginas
                    </h3>
                    <div class="row">
                        {% for child in children %}
                            <div class="col-md-6 mb-3">
                                <div class="card-django">
                                    <div class="card-body card-django-body-spacious card-django">
                                        <h5 class="card-title text-sans text-body">
                                            <a href="{% url 'pages:page_detail' child.slug %}" class="text-decoration-none">
                                                {{ child.title }}
                                            </a>
                                        </h5>
                                        {% if child.excerpt %}
                                            <p class="card-django-text text-secondary text-body">{{ child.excerpt|truncatewords:15 }}</p>
                                        {% endif %}
                                        <a href="{% url 'pages:page_detail' child.slug %}" class="btn btn-outline-primary btn-sm btn-enhanced">
                                            <i class="fas fa-arrow-right me-1"></i>Ler mais
                                        </a>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </section>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Related Pages -->
            {% if related_pages %}
                <div class="card-django mb-4">
                    <div class="card-header card-django-header-comfortable card-django">
                        <h5 class="mb-0 text-sans text-body">
                            <i class="fas fa-link me-2"></i>Páginas Relacionadas
                        </h5>
                    </div>
                    <div class="card-body card-django-body-spacious card-django">
                        {% for related in related_pages %}
                            <div class="d-flex mb-3">
                                {% if related.featured_image %}
                                    <img src="{{ related.featured_image.url }}" class="me-3 rounded" 
                                         style="width: 60px; height: 60px; object-fit: cover;" alt="{{ related.title }}">
                                {% else %}
                                    <div class="me-3 bg-secondary rounded d-flex align-items-center justify-content-center" 
                                         style="width: 60px; height: 60px;">
                                        <i class="fas fa-file-alt text-secondary"></i>
                                    </div>
                                {% endif %}
                                
                                <div class="flex-grow-1">
                                    <h6 class="mb-1 text-sans text-body">
                                        <a href="{% url 'pages:page_detail' related.slug %}" class="text-decoration-none">
                                            {{ related.title|truncatechars:40 }}
                                        </a>
                                    </h6>
                                    <small class="text-secondary">
                                        {{ related.created_at|date:"d/m/Y" }}
                                    </small>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}

            <!-- Table of Contents (if page has headings) -->
            <div class="card-django mb-4">
                <div class="card-header card-django-header-comfortable card-django">
                    <h5 class="mb-0 text-sans text-body">
                        <i class="fas fa-list me-2"></i>Nesta Página
                    </h5>
                </div>
                <div class="card-body card-django-body-spacious card-django">
                    <div id="table table-django-of-contents">
                        <!-- Will be populated by JavaScript -->
                        <p class="text-secondary mb-0 text-body">Carregando índice...</p>
                    </div>
                </div>
            </div>

            <!-- Back to Top -->
            <div class="text-center">
                <button onclick="window.scrollTo({top: 0, behavior: 'smooth'})" 
                        class="btn btn-outline-secondary btn-sm text-sans">
                    <i class="fas fa-arrow-up me-1"></i>Voltar ao topo
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Generate table table-django of contents
    const headings = document.querySelectorAll('.page-content h1, .page-content h2, .page-content h3, .page-content h4');
    const tocContainer = document.getElementById('table table-django-of-contents');
    
    if (headings.length > 0) {
        let tocHTML = '<ul class="list-unstyled">';
        
        headings.forEach(function(heading, index) {
            const id = 'heading-' + index;
            heading.id = id;
            
            const level = parseInt(heading.tagName.charAt(1));
            const indent = (level - 1) * 15;
            
            tocHTML += `
                <li style="margin-left: ${indent}px;" class="mb-1">
                    <a href="#${id}" class="text-decoration-none text-secondary">
                        ${heading.textContent}
                    </a>
                </li>
            `;
        });
        
        tocHTML += '</ul>';
        tocContainer.innerHTML = tocHTML;
        
        // Smooth scroll for TOC links
        tocContainer.querySelectorAll('a').forEach(function(link) {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    } else {
        tocContainer.innerHTML = '<p class="text-secondary mb-0 text-body">Nenhum cabeçalho encontrado.</p>';
    }
});
</script>
{% endblock %}

# ==============================================================================
# AMBIENTE
# ==============================================================================
ENVIRONMENT=development
DEBUG=True

# ==============================================================================
# SECRET KEY
# ==============================================================================
DJANGO_SECRET_KEY=troque-por-uma-chave-secreta-forte

# ==============================================================================
# HOSTS (liberado para qualquer origem)
# ==============================================================================
ALLOWED_HOSTS=*
CSRF_TRUSTED_ORIGINS=http://*

# ==============================================================================
# EMAIL
# ==============================================================================
EMAIL_BACKEND='django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST='smtp.gmail.com'
EMAIL_PORT='587'
EMAIL_HOST_USER='<EMAIL>'
EMAIL_HOST_PASSWORD='kwbqmparzimpwpqe'
EMAIL_USE_TLS='True'
EMAIL_USE_SSL='False'
DEFAULT_FROM_EMAIL='<EMAIL>'

# EMAIL_TIMEOUT = 10  # opcional: tempo em segundos para timeout
EMAIL_TIMEOUT='30'


# ==============================================================================
# BANCO DE DADOS (PostgreSQL)
# ==============================================================================
DB_ENGINE=django.db.backends.postgresql
DB_NAME=project_nix
DB_USER=project_nix_user
DB_PASSWORD=senha_segura
DB_HOST=localhost
DB_PORT=5432

# ==============================================================================
# CACHE
# ==============================================================================
REDIS_URL=redis://localhost:6379/1

# ==============================================================================
# SESSÃO E CSRF
# ==============================================================================
SESSION_COOKIE_AGE=86400
SESSION_EXPIRE_AT_BROWSER_CLOSE=False

# ==============================================================================
# UPLOADS E ARQUIVOS
# ==============================================================================
MAX_UPLOAD_SIZE=5242880
ALLOWED_IMAGE_EXTENSIONS=.jpg,.jpeg,.png,.gif,.webp
ALLOWED_DOCUMENT_EXTENSIONS=.pdf,.doc,.docx,.txt

# ==============================================================================
# PAGINAÇÃO
# ==============================================================================
PAGINATE_BY=12
ARTICLES_PER_PAGE=12
USERS_PER_PAGE=20

# ==============================================================================
# SITE
# ==============================================================================
SITE_NAME=Project Nix
SITE_DESCRIPTION=Sistema de gerenciamento de conteúdo moderno
SITE_URL=http://localhost:8000

# ==============================================================================
# REDES SOCIAIS
# ==============================================================================
FACEBOOK_URL=
TWITTER_URL=
LINKEDIN_URL=
GITHUB_URL=

# ==============================================================================
# ANALYTICS
# ==============================================================================
GOOGLE_ANALYTICS_ID=
GOOGLE_TAG_MANAGER_ID=
FACEBOOK_PIXEL_ID=

# ==============================================================================
# PROJECT NIX PERSONALIZADO
# ==============================================================================
ACTIVE_MODULES=accounts,config,pages,articles
DEFAULT_THEME=light
DEFAULT_LANGUAGE=pt-br
DEFAULT_TIMEZONE=America/Sao_Paulo
BACKUP_DIR=/var/www/project-nix/backups
BACKUP_RETENTION_DAYS=30

# ==============================================================================
# MONITORAMENTO
# ==============================================================================
SENTRY_DSN=
HEALTH_CHECK_ENABLED=True

# ==============================================================================
# DESENVOLVIMENTO
# ==============================================================================
DJANGO_DEBUG_TOOLBAR=True
SHOW_SQL_QUERIES=False

# ==============================================================================
# LOGGING
# ==============================================================================
LOG_LEVEL=DEBUG
LOG_FILE=/var/www/project-nix/logs/project-nix.log
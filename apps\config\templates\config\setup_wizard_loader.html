<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup Wizard - FireFlies</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        .wizard-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .wizard-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 1200px;
            min-height: 600px;
            overflow: hidden;
        }

        .wizard-header {
            background: linear-gradient(135deg, #0275b8 0%, #0056b3 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .wizard-header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
        }

        .wizard-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .wizard-body {
            display: flex;
            min-height: 500px;
        }

        .wizard-sidebar {
            width: 300px;
            background: #34495e;
            color: white;
            padding: 2rem 0;
        }

        .wizard-content {
            flex: 1;
            padding: 2rem;
            overflow-y: auto;
        }

        .step-item {
            padding: 1.5rem 2rem;
            margin: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }

        .step-item:hover {
            background: rgba(255,255,255,0.1);
        }

        .step-item.active {
            background: #0275b8;
            border-left-color: #fff;
        }

        .step-item.completed {
            background: rgba(40, 167, 69, 0.1);
            border-left-color: #28a745;
        }

        .step-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 1.2rem;
        }

        .step-item.active .step-icon {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .step-item.completed .step-icon {
            background: #28a745;
            color: white;
        }

        .step-item:not(.active):not(.completed) .step-icon {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.7);
        }

        .step-info h5 {
            color: white;
            font-weight: 600;
            margin: 0;
            font-size: 1rem;
        }

        .step-info p {
            color: rgba(255, 255, 255, 0.7);
            margin: 0.25rem 0 0 0;
            font-size: 0.85rem;
        }

        .step-content {
            display: none;
        }

        .step-content.active {
            display: block !important;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: #2c3e50;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 0.75rem;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            border-color: #0275b8;
            box-shadow: 0 0 0 0.2rem rgba(2, 117, 184, 0.25);
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .btn-primary {
            background: linear-gradient(135deg, #0275b8, #0056b3);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #0056b3, #004085);
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            color: white;
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #1e7e34, #155724);
        }

        .btn-info {
            background: #007bff;
            color: white;
        }

        .btn-info:hover {
            background: #0056b3;
        }

        .wizard-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e9ecef;
        }

        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #0275b8;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="wizard-container">
        <div class="wizard-card">
            <!-- Header -->
            <div class="wizard-header">
                <h1><i class="fas fa-magic me-3"></i>Setup Wizard</h1>
                <p>Configure o FireFlies em poucos passos</p>
            </div>
            
            <!-- Body -->
            <div class="wizard-body">
                <!-- Sidebar -->
                <div class="wizard-sidebar">
                    <div class="step-item active" data-step="1">
                        <div class="d-flex align-items-center">
                            <div class="step-icon">1</div>
                            <div class="step-info">
                                <h5>Bem-vindo</h5>
                                <p>Introdução ao sistema</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="step-item" data-step="2">
                        <div class="d-flex align-items-center">
                            <div class="step-icon">2</div>
                            <div class="step-info">
                                <h5>Banco de Dados</h5>
                                <p>Configure a conexão</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="step-item" data-step="3">
                        <div class="d-flex align-items-center">
                            <div class="step-icon">3</div>
                            <div class="step-info">
                                <h5>Administrador</h5>
                                <p>Crie o usuário admin</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="step-item" data-step="4">
                        <div class="d-flex align-items-center">
                            <div class="step-icon">4</div>
                            <div class="step-info">
                                <h5>Email</h5>
                                <p>Configure o envio</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="step-item" data-step="5">
                        <div class="d-flex align-items-center">
                            <div class="step-icon">5</div>
                            <div class="step-info">
                                <h5>Segurança</h5>
                                <p>Defina as opções</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="step-item" data-step="6">
                        <div class="d-flex align-items-center">
                            <div class="step-icon">6</div>
                            <div class="step-info">
                                <h5>Finalizar</h5>
                                <p>Concluir configuração</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Main Content -->
                <div class="wizard-content">
                    <!-- Step 1: Welcome -->
                    <div class="step-content active" id="step-1">
                        <h2 class="mb-4">Bem-vindo ao FireFlies!</h2>
                        <p class="lead">Este assistente irá ajudá-lo a configurar seu sistema em poucos passos simples.</p>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Dica:</strong> O wizard detectará automaticamente bancos de dados existentes e fará recomendações inteligentes.
                        </div>

                        <div class="row mt-4">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <i class="fas fa-database fa-3x text-primary mb-3"></i>
                                    <h5>Banco de Dados</h5>
                                    <p class="text-muted">Configure a conexão com seu banco de dados</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <i class="fas fa-user-shield fa-3x text-success mb-3"></i>
                                    <h5>Administrador</h5>
                                    <p class="text-muted">Crie o usuário administrador do sistema</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <i class="fas fa-envelope fa-3x text-warning mb-3"></i>
                                    <h5>Email</h5>
                                    <p class="text-muted">Configure o sistema de email</p>
                                </div>
                            </div>
                        </div>

                        <div class="wizard-actions">
                            <div></div>
                            <button class="btn btn-primary" onclick="nextStep()">
                                Começar <i class="fas fa-arrow-right ms-1"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Step 2: Database -->
                    <div class="step-content" id="step-2">
                        <h2 class="mb-4">Configuração do Banco de Dados</h2>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Tipo de Banco</label>
                                    <select class="form-control" id="database-type">
                                        <option value="sqlite">SQLite (Recomendado para desenvolvimento)</option>
                                        <option value="postgresql">PostgreSQL (Recomendado para produção)</option>
                                        <option value="mysql">MySQL</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Nome do Banco</label>
                                    <input type="text" class="form-control" id="database-name" placeholder="fireflies">
                                </div>
                            </div>
                        </div>
                        
                        <!-- PostgreSQL/MySQL specific fields -->
                        <div id="network-fields" style="display: none;">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Host</label>
                                        <input type="text" class="form-control" id="database-host" placeholder="localhost">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Porta</label>
                                        <input type="text" class="form-control" id="database-port" placeholder="5432">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Usuário</label>
                                        <input type="text" class="form-control" id="database-user" placeholder="postgres">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Senha</label>
                                        <input type="password" class="form-control" id="database-password">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="db-test-feedback" class="mt-2"></div>

                        <div class="wizard-actions">
                            <button class="btn btn-secondary" onclick="prevStep()">
                                <i class="fas fa-arrow-left me-1"></i>Anterior
                            </button>
                            <button class="btn btn-info" type="button" onclick="testDatabaseConnection()">
                                <i class="fas fa-vial me-1"></i>Testar conexão
                            </button>
                            <button class="btn btn-primary" onclick="nextStep()">
                                Próximo <i class="fas fa-arrow-right ms-1"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Step 3: Admin -->
                    <div class="step-content" id="step-3">
                        <h2 class="mb-4">Usuário Administrador</h2>
                        <p class="lead">Crie o usuário administrador do sistema.</p>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Nome de Usuário</label>
                                    <input type="text" class="form-control" id="admin-username" placeholder="admin">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Email</label>
                                    <input type="email" class="form-control" id="admin-email" placeholder="<EMAIL>">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Senha</label>
                                    <input type="password" class="form-control" id="admin-password" placeholder="Senha forte">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Confirmar Senha</label>
                                    <input type="password" class="form-control" id="admin-password-confirm" placeholder="Confirme a senha">
                                </div>
                            </div>
                        </div>

                        <div class="wizard-actions">
                            <button class="btn btn-secondary" onclick="prevStep()">
                                <i class="fas fa-arrow-left me-1"></i>Anterior
                            </button>
                            <button class="btn btn-primary" onclick="nextStep()">
                                Próximo <i class="fas fa-arrow-right ms-1"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Step 4: Email -->
                    <div class="step-content" id="step-4">
                        <h2 class="mb-4">Configuração de Email</h2>
                        <p class="lead">Configure o sistema de email para notificações.</p>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Servidor SMTP</label>
                                    <input type="text" class="form-control" id="email-host" placeholder="smtp.gmail.com">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Porta</label>
                                    <input type="text" class="form-control" id="email-port" placeholder="587">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Email</label>
                                    <input type="email" class="form-control" id="email-address" placeholder="<EMAIL>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Senha</label>
                                    <input type="password" class="form-control" id="email-password" placeholder="Senha do email">
                                </div>
                            </div>
                        </div>

                        <div class="wizard-actions">
                            <button class="btn btn-secondary" onclick="prevStep()">
                                <i class="fas fa-arrow-left me-1"></i>Anterior
                            </button>
                            <button class="btn btn-primary" onclick="nextStep()">
                                Próximo <i class="fas fa-arrow-right ms-1"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Step 5: Security -->
                    <div class="step-content" id="step-5">
                        <h2 class="mb-4">Configurações de Segurança</h2>
                        <p class="lead">Configure as opções de segurança do sistema.</p>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Chave Secreta</label>
                                    <input type="text" class="form-control" id="secret-key" placeholder="Chave secreta do Django">
                                    <small class="form-text text-muted">Deixe em branco para gerar automaticamente</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Debug Mode</label>
                                    <select class="form-control" id="debug-mode">
                                        <option value="True">Ativado (Desenvolvimento)</option>
                                        <option value="False">Desativado (Produção)</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="wizard-actions">
                            <button class="btn btn-secondary" onclick="prevStep()">
                                <i class="fas fa-arrow-left me-1"></i>Anterior
                            </button>
                            <button class="btn btn-primary" onclick="nextStep()">
                                Próximo <i class="fas fa-arrow-right ms-1"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Step 6: Finalize -->
                    <div class="step-content" id="step-6">
                        <h2 class="mb-4">Finalizar Configuração</h2>
                        <p class="lead">Revise as configurações e finalize a instalação.</p>
                        
                        <div id="finalize-summary">
                            <!-- Summary will be populated by JavaScript -->
                        </div>

                        <div class="wizard-actions">
                            <button class="btn btn-secondary" onclick="prevStep()">
                                <i class="fas fa-arrow-left me-1"></i>Anterior
                            </button>
                            <button class="btn btn-success" onclick="finalizeSetup()" id="finalize-btn">
                                <i class="fas fa-check me-1"></i>Finalizar Configuração
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let currentStep = 1;
        const totalSteps = 6;
        let setupData = {};

        // Initialize wizard
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Wizard inicializado');
            
            // Add database type change handler
            const databaseTypeSelect = document.getElementById('database-type');
            if (databaseTypeSelect) {
                databaseTypeSelect.addEventListener('change', function() {
                    const type = this.value;
                    const networkFields = document.getElementById('network-fields');
                    
                    if (type === 'sqlite') {
                        networkFields.style.display = 'none';
                    } else {
                        networkFields.style.display = 'block';
                        document.getElementById('database-port').value = type === 'postgresql' ? '5432' : '3306';
                        document.getElementById('database-user').value = type === 'postgresql' ? 'postgres' : 'root';
                    }
                });
            }
            
            // Add click handlers to sidebar steps
            document.querySelectorAll('.step-item').forEach(item => {
                item.addEventListener('click', function() {
                    const step = parseInt(this.dataset.step);
                    if (step <= currentStep) {
                        goToStep(step);
                    }
                });
            });
        });

        function goToStep(step) {
            console.log('Indo para o passo:', step);
            currentStep = step;
            showStep(step);
        }

        function showStep(step) {
            console.log('Mostrando passo:', step);
            
            // Hide all steps
            document.querySelectorAll('.step-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Show current step
            const currentStepElement = document.getElementById('step-' + step);
            if (currentStepElement) {
                currentStepElement.classList.add('active');
                console.log('Passo', step, 'ativado');
            } else {
                console.error('Elemento do passo', step, 'não encontrado');
            }
            
            // Update sidebar
            document.querySelectorAll('.step-item').forEach(item => {
                item.classList.remove('active');
                if (parseInt(item.dataset.step) < step) {
                    item.classList.add('completed');
                } else if (parseInt(item.dataset.step) === step) {
                    item.classList.add('active');
                }
            });
            
            // Load step-specific data
            if (step === 6) {
                loadFinalizeStep();
            }
        }

        function nextStep() {
            console.log('Próximo passo chamado. Passo atual:', currentStep);
            // Validação obrigatória no passo admin
            if (currentStep === 3) {
                const username = document.getElementById('admin-username').value.trim();
                const password = document.getElementById('admin-password').value;
                const passwordConfirm = document.getElementById('admin-password-confirm').value;
                if (!username || !password) {
                    alert('Preencha o nome de usuário e a senha do administrador.');
                    return;
                }
                if (password !== passwordConfirm) {
                    alert('As senhas não coincidem.');
                    return;
                }
            }
            if (currentStep < totalSteps) {
                currentStep++;
                showStep(currentStep);
            }
        }

        function prevStep() {
            console.log('Passo anterior chamado. Passo atual:', currentStep);
            if (currentStep > 1) {
                currentStep--;
                showStep(currentStep);
            }
        }

        function loadFinalizeStep() {
            // Collect all form data
            setupData = {
                database: {
                    type: document.getElementById('database-type')?.value || 'sqlite',
                    name: document.getElementById('database-name')?.value || 'fireflies',
                    host: document.getElementById('database-host')?.value || '',
                    port: document.getElementById('database-port')?.value || '',
                    user: document.getElementById('database-user')?.value || '',
                    password: document.getElementById('database-password')?.value || ''
                },
                admin: {
                    username: document.getElementById('admin-username')?.value || '',
                    email: document.getElementById('admin-email')?.value || '',
                    password: document.getElementById('admin-password')?.value || ''
                },
                email: {
                    host: document.getElementById('email-host')?.value || '',
                    port: document.getElementById('email-port')?.value || '',
                    address: document.getElementById('email-address')?.value || '',
                    password: document.getElementById('email-password')?.value || ''
                },
                security: {
                    secret_key: document.getElementById('secret-key')?.value || '',
                    debug_mode: document.getElementById('debug-mode')?.value || 'True'
                }
            };
            
            // Populate summary
            const summaryContainer = document.getElementById('finalize-summary');
            if (summaryContainer) {
                summaryContainer.innerHTML = `
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle me-2"></i>Resumo da Configuração</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Banco de Dados:</strong> ${setupData.database.type.toUpperCase()}<br>
                                <strong>Nome:</strong> ${setupData.database.name}<br>
                                ${setupData.database.host ? `<strong>Host:</strong> ${setupData.database.host}<br>` : ''}
                                ${setupData.database.port ? `<strong>Porta:</strong> ${setupData.database.port}<br>` : ''}
                            </div>
                            <div class="col-md-6">
                                <strong>Admin:</strong> ${setupData.admin.username}<br>
                                <strong>Email:</strong> ${setupData.admin.email}<br>
                                <strong>Debug:</strong> ${setupData.security.debug_mode}<br>
                            </div>
                        </div>
                    </div>
                `;
            }
        }

        function finalizeSetup() {
            const finalizeBtn = document.getElementById('finalize-btn');
            if (finalizeBtn) {
                finalizeBtn.disabled = true;
                finalizeBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Finalizando...';
            }
            
            // Get CSRF token
            function getCookie(name) {
                let cookieValue = null;
                if (document.cookie && document.cookie !== '') {
                    const cookies = document.cookie.split(';');
                    for (let i = 0; i < cookies.length; i++) {
                        const cookie = cookies[i].trim();
                        if (cookie.substring(0, name.length + 1) === (name + '=')) {
                            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                            break;
                        }
                    }
                }
                return cookieValue;
            }
            
            const csrfToken = getCookie('csrftoken');
            
            // Send data to backend
            fetch('/config/setup-wizard/api/finalize/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken || ''
                },
                body: JSON.stringify(setupData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Redirect to home page
                    window.location.href = '/';
                } else {
                    alert('Erro ao finalizar configuração: ' + (data.error || 'Erro desconhecido'));
                    if (finalizeBtn) {
                        finalizeBtn.disabled = false;
                        finalizeBtn.innerHTML = '<i class="fas fa-check me-1"></i>Finalizar Configuração';
                    }
                }
            })
            .catch(error => {
                console.error('Error finalizing setup:', error);
                alert('Erro ao finalizar configuração. Tente novamente.');
                if (finalizeBtn) {
                    finalizeBtn.disabled = false;
                    finalizeBtn.innerHTML = '<i class="fas fa-check me-1"></i>Finalizar Configuração';
                }
            });
        }

        function testDatabaseConnection() {
            const type = document.getElementById('database-type').value;
            const name = document.getElementById('database-name').value;
            const host = document.getElementById('database-host')?.value || '';
            const port = document.getElementById('database-port')?.value || '';
            const user = document.getElementById('database-user')?.value || '';
            const password = document.getElementById('database-password')?.value || '';

            const data = { type, name, host, port, user, password };
            function getCookie(name) {
                let cookieValue = null;
                if (document.cookie && document.cookie !== '') {
                    const cookies = document.cookie.split(';');
                    for (let i = 0; i < cookies.length; i++) {
                        const cookie = cookies[i].trim();
                        if (cookie.substring(0, name.length + 1) === (name + '=')) {
                            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                            break;
                        }
                    }
                }
                return cookieValue;
            }
            const csrfToken = getCookie('csrftoken');
            const btn = event.target;
            const feedback = document.getElementById('db-test-feedback');
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Testando...';
            feedback.innerHTML = '<span class="text-info"><i class="fas fa-spinner fa-spin me-1"></i>Testando conexão...</span>';

            fetch('/config/setup-wizard/api/test-db/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken || ''
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    feedback.innerHTML = '<span class="text-success"><i class="fas fa-check-circle me-1"></i>Conexão bem-sucedida!</span>';
                } else {
                    feedback.innerHTML = `<span class="text-danger"><i class="fas fa-times-circle me-1"></i>Falha na conexão: ${data.error || 'Erro desconhecido'}</span>`;
                }
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-vial me-1"></i>Testar conexão';
            })
            .catch(error => {
                feedback.innerHTML = '<span class="text-danger"><i class="fas fa-times-circle me-1"></i>Erro ao testar conexão.</span>';
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-vial me-1"></i>Testar conexão';
            });
        }

        // Limpar feedback ao editar qualquer campo do banco
        ['database-type','database-name','database-host','database-port','database-user','database-password'].forEach(function(id) {
            const el = document.getElementById(id);
            if (el) {
                el.addEventListener('input', function() {
                    const feedback = document.getElementById('db-test-feedback');
                    if (feedback) feedback.innerHTML = '';
                });
            }
        });
    </script>
</body>
</html>
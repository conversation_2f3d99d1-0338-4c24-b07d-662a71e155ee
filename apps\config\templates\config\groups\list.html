{% extends 'config/base_config_page.html' %}
{% load config_extras %}

{% block config_title %}Gerenciar Grupos{% endblock %}
{% block page_icon %}<i class="fas fa-layer-group me-2"></i>{% endblock %}
{% block page_title %}Gerenciar Grupos{% endblock %}
{% block page_actions %}
<a href="{% url 'config:group_create' %}" class="btn btn-primary">
    <i class="fas fa-plus me-2"></i>Novo Grupo
</a>
{% endblock %}

{% block page_content %}
<div class="card shadow-sm">
    <div class="card-body p-0">
        <div class="table-responsive d-none d-md-block">
            <table class="table table-hover align-middle mb-0">
                <thead class="table-light">
                    <tr>
                        <th scope="col"><i class="fas fa-layer-group"></i> Nome</th>
                        <th scope="col"><i class="fas fa-users"></i> Membros</th>
                        <th scope="col"><i class="fas fa-shield-alt"></i> Permissões</th>
                        <th scope="col"><i class="fas fa-calendar"></i> Criado em</th>
                        <th scope="col"><i class="fas fa-cogs"></i> Ações</th>
                    </tr>
                </thead>
                <tbody>
                {% for group in groups %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm me-3">
                                    <div class="bg-django-green rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                        <i class="fas fa-layer-group text-white"></i>
                                    </div>
                                </div>
                                <div>
                                    <div class="fw-bold">{{ group.name }}</div>
                                    <small class="text-muted">{{ group.description|default:"Sem descrição" }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-info">{{ group.user_set.count }} usuário{{ group.user_set.count|pluralize:"s" }}</span>
                        </td>
                        <td>
                            <span class="badge bg-secondary">{{ group.permissions.count }} permissão{{ group.permissions.count|pluralize:"ões" }}</span>
                        </td>
                        <td>{{ group.created_at|date:"d/m/Y"|default:"—" }}</td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{% url 'config:group_detail' group.slug %}" class="btn btn-outline-info" title="Detalhes">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'config:group_update' group.slug %}" class="btn btn-outline-primary" title="Editar">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'config:group_delete' group.slug %}" class="btn btn-outline-danger" title="Deletar">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                {% empty %}
                    <tr><td colspan="5" class="text-center text-muted">Nenhum grupo encontrado.</td></tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Layout responsivo para mobile -->
        <div class="d-block d-md-none">
            {% for group in groups %}
            <div class="card mb-2">
                <div class="card-body py-2 px-3">
                    <div class="d-flex align-items-center mb-2">
                        <div class="avatar-sm me-3">
                            <div class="bg-django-green rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                <i class="fas fa-layer-group text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <div class="fw-bold">{{ group.name }}</div>
                            <small class="text-muted">{{ group.description|default:"Sem descrição" }}</small>
                        </div>
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-info me-2">{{ group.user_set.count }} usuário{{ group.user_set.count|pluralize:"s" }}</span>
                        <span class="badge bg-secondary">{{ group.permissions.count }} permissão{{ group.permissions.count|pluralize:"ões" }}</span>
                    </div>
                    <div class="mb-2 small text-muted">
                        Criado em: {{ group.created_at|date:"d/m/Y"|default:"—" }}
                    </div>
                    <div class="btn-group btn-group-sm w-100" role="group">
                        <a href="{% url 'config:group_detail' group.slug %}" class="btn btn-outline-info w-100" title="Detalhes">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="{% url 'config:group_update' group.slug %}" class="btn btn-outline-primary w-100" title="Editar">
                            <i class="fas fa-edit"></i>
                        </a>
                        <a href="{% url 'config:group_delete' group.slug %}" class="btn btn-outline-danger w-100" title="Deletar">
                            <i class="fas fa-trash"></i>
                        </a>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="text-center text-muted">Nenhum grupo encontrado.</div>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %} 
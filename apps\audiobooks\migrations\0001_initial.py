# Generated by Django 5.2.2 on 2025-07-22 21:00

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Audiobook',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='T<PERSON>tu<PERSON>')),
                ('author', models.CharField(blank=True, max_length=120, verbose_name='Autor')),
                ('narrator', models.CharField(blank=True, max_length=120, verbose_name='Narrador')),
                ('description', models.TextField(blank=True, verbose_name='Descrição')),
                ('published_date', models.DateField(blank=True, null=True, verbose_name='Data de Publicação')),
                ('duration', models.DurationField(blank=True, null=True, verbose_name='Dura<PERSON>')),
                ('cover_image', models.ImageField(blank=True, null=True, upload_to='audiobooks/covers/', verbose_name='Capa')),
                ('audio_file', models.FileField(blank=True, null=True, upload_to='audiobooks/files/', verbose_name='Arquivo de Áudio')),
                ('slug', models.SlugField(blank=True, unique=True, verbose_name='Slug')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Criado em')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Atualizado em')),
            ],
            options={
                'verbose_name': 'Audiolivro',
                'verbose_name_plural': 'Audiolivros',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='AudiobookFavorite',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Adicionado em')),
                ('audiobook', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='audiobooks.audiobook')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Audiolivro Favorito',
                'verbose_name_plural': 'Audiolivros Favoritos',
                'unique_together': {('user', 'audiobook')},
            },
        ),
        migrations.CreateModel(
            name='AudiobookProgress',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('current_time', models.DurationField(default='00:00:00', verbose_name='Tempo Atual')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Atualizado em')),
                ('audiobook', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='audiobooks.audiobook')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Progresso de Audiolivro',
                'verbose_name_plural': 'Progressos de Audiolivros',
                'unique_together': {('user', 'audiobook')},
            },
        ),
    ]
